class CreatePayments < ActiveRecord::Migration[8.0]
  def change
    create_table :payments do |t|
      t.references :user, null: false, foreign_key: true
      t.references :subscription, null: true, foreign_key: true
      t.string :stripe_payment_intent_id, null: false
      t.string :stripe_charge_id
      t.integer :amount_cents, null: false
      t.string :currency, null: false, default: 'usd'
      t.string :status, null: false # succeeded, pending, failed, etc.
      t.text :description
      t.text :metadata # JSON for additional data
      t.datetime :paid_at
      t.string :payment_method_type # card, bank_transfer, etc.
      t.string :failure_reason

      t.timestamps
    end

    add_index :payments, :stripe_payment_intent_id, unique: true
    add_index :payments, :stripe_charge_id
    add_index :payments, :status
    add_index :payments, [:user_id, :status]
    add_index :payments, :paid_at
  end
end
