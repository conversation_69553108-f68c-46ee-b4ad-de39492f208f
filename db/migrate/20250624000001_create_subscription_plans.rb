class CreateSubscriptionPlans < ActiveRecord::Migration[8.0]
  def change
    create_table :subscription_plans do |t|
      t.string :name, null: false
      t.string :stripe_price_id, null: false
      t.integer :price_cents, null: false, default: 0
      t.string :currency, null: false, default: 'usd'
      t.string :interval, null: false # 'month', 'year'
      t.text :features # JSON array of features
      t.boolean :active, null: false, default: true
      t.text :description
      t.integer :sort_order, default: 0

      t.timestamps
    end

    add_index :subscription_plans, :stripe_price_id, unique: true
    add_index :subscription_plans, :active
  end
end
