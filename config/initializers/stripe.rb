# Stripe configuration
Rails.configuration.stripe = {
  publishable_key: Rails.application.credentials.dig(:stripe, :publishable_key) || ENV["STRIPE_PUBLISHABLE_KEY"],
  secret_key: Rails.application.credentials.dig(:stripe, :secret_key) || ENV["STRIPE_SECRET_KEY"],
  webhook_secret: Rails.application.credentials.dig(:stripe, :webhook_secret) || ENV["STRIPE_WEBHOOK_SECRET"]
}

# Set the Stripe API key
Stripe.api_key = Rails.configuration.stripe[:secret_key]

# Set API version for consistency
Stripe.api_version = "2024-12-18.acacia"

# Configure logging in development
if Rails.env.development?
  Stripe.log_level = Stripe::LEVEL_INFO
end
