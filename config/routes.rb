Rails.application.routes.draw do
  resources :templates
  resources :resumes do
    resources :experiences, :educations, :projects, :skills, :languages, :references, :hobbies, :volunteerings, :awards, :certifications, :profiles
  end
  resource :session
  resources :passwords, param: :token
  post "signup", to: "sessions#signup"

  # Payment and subscription routes
  resources :subscriptions, only: [ :index, :show, :create ] do
    member do
      post :cancel
      post :pause
      post :resume
    end
    collection do
      get :plans
    end
  end

  resources :payments, only: [ :index, :show ] do
    member do
      post :refund
    end
    collection do
      post :create_intent
      get :setup_intent
      get :payment_methods
      delete "payment_methods/:payment_method_id", to: "payments#detach_payment_method"
    end
  end

  # Stripe webhooks
  post "/stripe/webhooks", to: "stripe_webhooks#create"

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Defines the root path route ("/")
  # root "posts#index"
end
