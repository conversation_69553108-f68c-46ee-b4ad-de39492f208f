export const referenceSchema = {
  type: "object",
  collection: "references",
  description: "Add your references",
  entity: "reference",
  properties: {
    name: { type: "string", placeholder: "Full name" },
    company: { type: "string", placeholder: "Company" },
    position: { type: "string", placeholder: "Position" },
    email: { type: "string", placeholder: "Email address" },
    phone: { type: "string", placeholder: "Phone number" },
    description: { type: "textarea", label: "Description" },
  },
  required: ["name", "email"],
};

export const projectSchema = {
  type: "object",
  collection: "projects",
  description: "Add your projects",
  entity: "project",
  properties: {
    title: { type: "string", placeholder: "Project title" },
    client: { type: "string", placeholder: "Client" },
    start_date: { type: "date", placeholder: "From" },
    end_date: { type: "date", placeholder: "To" },
    url: {
      type: ["string", "null"],
      placeholder: "https://www.example.com",
    },
    description: { type: "textarea", label: "Description" },
  },
  required: ["title"],
};

export const languageSchema = {
  type: "object",
  collection: "languages",
  description: "Add your languages",
  entity: "language",
  properties: {
    name: { type: "string", placeholder: "Language name" },
    proficiency: {
      type: "number",
      placeholder: "Proficiency level (1-100)",
    },
  },
  required: ["name"],
};

export const hobbySchema = {
  type: "object",
  collection: "hobbies",
  description: "Add your hobbies",
  entity: "hobby",
  properties: {
    name: {
      type: "string",
      placeholder: "Hobby name",
      className: "col-span-full",
    },
  },
  required: ["name"],
};

export const experienceSchema = {
  type: "object",
  collection: "experiences",
  description: "Add your experiences",
  entity: "experience",
  properties: {
    company: { type: "string", placeholder: "Company Name" },
    title: { type: "string", placeholder: "Job Title" },
    city: { type: "string", placeholder: "City" },
    country: { type: "string", placeholder: "Country" },
    start_date: { type: "date", placeholder: "From" },
    end_date: { type: "date", placeholder: "To" },
    is_current: { type: "boolean", label: "I am currently employed here" },
    description: { type: "textarea", label: "Description" },
  },
  required: ["company", "title"],
};

export const educationSchema = {
  type: "object",
  collection: "educations",
  description: "Add your educational background",
  entity: "education",
  properties: {
    institution: { type: "string", placeholder: "Institution name" },
    field_of_study: { type: "string", placeholder: "Field of Study" },
    degree: { type: "string", placeholder: "Degree" },
    city: { type: "string", placeholder: "City" },
    country: { type: "string", placeholder: "Country" },
    start_date: { type: "date", placeholder: "From" },
    end_date: { type: "date", placeholder: "To" },
    website: {
      type: ["string", "null"],
      placeholder: "https://www.example.com",
    },
    is_current: { type: "boolean", label: "I currently study here" },
    description: { type: "textarea", label: "Description" },
  },
  required: ["institution", "field_of_study", "degree"],
};

export const certificationSchema = {
  type: "object",
  collection: "certifications",
  description: "Add your certifications",
  entity: "certification",
  properties: {
    title: { type: "string", placeholder: "Certification name" },
    issuer: { type: "string", placeholder: "Issuer" },
    url: {
      type: ["string", "null"],
      placeholder: "https://www.example.com",
    },
    date_received: {
      type: "date",
      placeholder: "Date of certification",
    },
    description: { type: "textarea", label: "Description" },
  },
  required: ["title", "issuer"],
};

export const awardSchema = {
  type: "object",
  collection: "awards",
  description: "Add your awards",
  entity: "award",
  properties: {
    title: { type: "string", placeholder: "Award title" },
    issuer: { type: "string", placeholder: "Issuer" },
    date_received: {
      type: "date",
      placeholder: "Date received",
    },
    url: {
      type: ["string", "null"],
      placeholder: "https://www.example.com",
    },
    description: { type: "textarea", label: "Description" },
  },
  required: ["title", "issuer"],
};

export const volunteeringSchema = {
  type: "object",
  collection: "volunteerings",
  description: "Add your volunteerings",
  entity: "volunteering",
  properties: {
    organization: { type: "string", placeholder: "Organization" },
    role: { type: "string", placeholder: "Role" },
    start_date: { type: "date", placeholder: "From" },
    end_date: { type: "date", placeholder: "To" },
    description: { type: "textarea", label: "Description" },
  },
  required: ["organization", "role"],
};

export const skillSchema = {
  type: "object",
  collection: "skills",
  description: "Add your skills",
  entity: "skill",
  properties: {
    name: { type: "string", placeholder: "Skill name" },
    category: { type: "string", placeholder: "Category" },
    proficiency: {
      type: "number",
      placeholder: "Proficiency level (1-100)",
    },
  },
  required: ["name"],
};

export const profilesSchema = {
  type: "object",
  collection: "profiles",
  description: "Add your profiles",
  entity: "profile",
  properties: {
    username: { type: "string", placeholder: "john.doe" },
    url: { type: "string", placeholder: "https://github.com/johndoe" },
    network: { type: "string", placeholder: "Github" },
    icon: { type: "string", placeholder: "github" },
  },
};
