"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  CardBody,
  <PERSON>H<PERSON><PERSON>,
  <PERSON><PERSON>,
  Chip,
  Divider,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import toast from "react-hot-toast";

interface SubscriptionPlan {
  id: number;
  name: string;
  price_cents: number;
  currency: string;
  interval: string;
  features: string[];
  description?: string;
  popular?: boolean;
}

interface SubscriptionCardProps {
  plan: SubscriptionPlan;
  onSubscribe: (planId: number) => Promise<void>;
  isLoading?: boolean;
  currentPlan?: boolean;
}

export const SubscriptionCard: React.FC<SubscriptionCardProps> = ({
  plan,
  onSubscribe,
  isLoading = false,
  currentPlan = false,
}) => {
  const [subscribing, setSubscribing] = useState(false);

  const formatPrice = (cents: number, currency: string) => {
    const amount = cents / 100;
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  const handleSubscribe = async () => {
    try {
      setSubscribing(true);
      await onSubscribe(plan.id);
    } catch (error) {
      toast.error("Failed to start subscription");
    } finally {
      setSubscribing(false);
    }
  };

  return (
    <Card
      className={`relative ${
        plan.popular
          ? "border-2 border-primary shadow-lg scale-105"
          : "border border-default-200"
      } ${currentPlan ? "bg-success-50 border-success-200" : ""}`}
    >
      {plan.popular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Chip color="primary" variant="solid" size="sm">
            Most Popular
          </Chip>
        </div>
      )}

      <CardHeader className="flex flex-col items-start gap-2 pt-6">
        <div className="flex items-center justify-between w-full">
          <h3 className="text-xl font-bold">{plan.name}</h3>
          {currentPlan && (
            <Chip color="success" variant="flat" size="sm">
              Current Plan
            </Chip>
          )}
        </div>
        
        <div className="flex items-baseline gap-1">
          <span className="text-3xl font-bold">
            {formatPrice(plan.price_cents, plan.currency)}
          </span>
          <span className="text-default-500">/{plan.interval}</span>
        </div>
        
        {plan.description && (
          <p className="text-sm text-default-600">{plan.description}</p>
        )}
      </CardHeader>

      <CardBody className="pt-0">
        <Divider className="mb-4" />
        
        <div className="space-y-3 mb-6">
          {plan.features.map((feature, index) => (
            <div key={index} className="flex items-center gap-2">
              <Icon
                icon="solar:check-circle-bold"
                className="text-success text-lg flex-shrink-0"
              />
              <span className="text-sm">{feature}</span>
            </div>
          ))}
        </div>

        <Button
          color={currentPlan ? "default" : plan.popular ? "primary" : "default"}
          variant={currentPlan ? "flat" : "solid"}
          fullWidth
          size="lg"
          onPress={handleSubscribe}
          isLoading={subscribing || isLoading}
          disabled={currentPlan || subscribing || isLoading}
          startContent={
            !subscribing && !isLoading && !currentPlan && (
              <Icon icon="solar:card-outline" className="text-lg" />
            )
          }
        >
          {currentPlan
            ? "Current Plan"
            : subscribing
            ? "Starting..."
            : `Subscribe to ${plan.name}`}
        </Button>
      </CardBody>
    </Card>
  );
};
