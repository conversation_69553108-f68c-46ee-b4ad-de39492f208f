"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Chip,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Modal,
  ModalContent,
  <PERSON>dal<PERSON>eader,
  <PERSON>dalBody,
  <PERSON>dal<PERSON><PERSON>er,
  useDisclosure,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import toast from "react-hot-toast";

interface Subscription {
  id: number;
  status: string;
  current_period_end: string;
  subscription_plan: {
    name: string;
    price_cents: number;
    currency: string;
    interval: string;
  };
}

interface Payment {
  id: number;
  amount_cents: number;
  currency: string;
  status: string;
  description: string;
  paid_at: string;
  payment_method_type: string;
}

interface BillingDashboardProps {
  subscription?: Subscription;
  payments: Payment[];
  onCancelSubscription?: () => Promise<void>;
  onPauseSubscription?: () => Promise<void>;
  onResumeSubscription?: () => Promise<void>;
}

export const BillingDashboard: React.FC<BillingDashboardProps> = ({
  subscription,
  payments,
  onCancelSubscription,
  onPauseSubscription,
  onResumeSubscription,
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [actionType, setActionType] = useState<"cancel" | "pause" | "resume">("cancel");
  const [isLoading, setIsLoading] = useState(false);

  const formatPrice = (cents: number, currency: string) => {
    const amount = cents / 100;
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "success";
      case "canceled":
        return "danger";
      case "past_due":
        return "warning";
      case "paused":
        return "default";
      default:
        return "default";
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "succeeded":
        return "success";
      case "canceled":
      case "failed":
        return "danger";
      case "pending":
        return "warning";
      default:
        return "default";
    }
  };

  const handleAction = async () => {
    try {
      setIsLoading(true);
      
      switch (actionType) {
        case "cancel":
          await onCancelSubscription?.();
          toast.success("Subscription canceled successfully");
          break;
        case "pause":
          await onPauseSubscription?.();
          toast.success("Subscription paused successfully");
          break;
        case "resume":
          await onResumeSubscription?.();
          toast.success("Subscription resumed successfully");
          break;
      }
      
      onClose();
    } catch (error) {
      toast.error(`Failed to ${actionType} subscription`);
    } finally {
      setIsLoading(false);
    }
  };

  const openModal = (type: "cancel" | "pause" | "resume") => {
    setActionType(type);
    onOpen();
  };

  return (
    <div className="space-y-6">
      {/* Current Subscription */}
      {subscription && (
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Icon icon="solar:card-outline" className="text-2xl text-primary" />
              <h2 className="text-xl font-semibold">Current Subscription</h2>
            </div>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold text-lg">{subscription.subscription_plan.name}</h3>
                <p className="text-2xl font-bold text-primary">
                  {formatPrice(subscription.subscription_plan.price_cents, subscription.subscription_plan.currency)}
                  <span className="text-sm font-normal text-default-500">
                    /{subscription.subscription_plan.interval}
                  </span>
                </p>
                <div className="flex items-center gap-2 mt-2">
                  <Chip color={getStatusColor(subscription.status)} variant="flat" size="sm">
                    {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                  </Chip>
                </div>
              </div>
              
              <div className="space-y-2">
                <p className="text-sm">
                  <span className="font-medium">Next billing date:</span>{" "}
                  {formatDate(subscription.current_period_end)}
                </p>
                
                <div className="flex gap-2 flex-wrap">
                  {subscription.status === "active" && (
                    <>
                      <Button
                        size="sm"
                        variant="flat"
                        color="warning"
                        onPress={() => openModal("pause")}
                        startContent={<Icon icon="solar:pause-outline" />}
                      >
                        Pause
                      </Button>
                      <Button
                        size="sm"
                        variant="flat"
                        color="danger"
                        onPress={() => openModal("cancel")}
                        startContent={<Icon icon="solar:close-circle-outline" />}
                      >
                        Cancel
                      </Button>
                    </>
                  )}
                  
                  {subscription.status === "paused" && (
                    <Button
                      size="sm"
                      variant="flat"
                      color="success"
                      onPress={() => openModal("resume")}
                      startContent={<Icon icon="solar:play-outline" />}
                    >
                      Resume
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Payment History */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Icon icon="solar:history-outline" className="text-2xl text-primary" />
            <h2 className="text-xl font-semibold">Payment History</h2>
          </div>
        </CardHeader>
        <CardBody>
          {payments.length === 0 ? (
            <div className="text-center py-8 text-default-500">
              <Icon icon="solar:file-text-outline" className="text-4xl mb-2" />
              <p>No payments found</p>
            </div>
          ) : (
            <Table aria-label="Payment history">
              <TableHeader>
                <TableColumn>DATE</TableColumn>
                <TableColumn>DESCRIPTION</TableColumn>
                <TableColumn>AMOUNT</TableColumn>
                <TableColumn>METHOD</TableColumn>
                <TableColumn>STATUS</TableColumn>
              </TableHeader>
              <TableBody>
                {payments.map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell>{formatDate(payment.paid_at)}</TableCell>
                    <TableCell>{payment.description}</TableCell>
                    <TableCell>
                      {formatPrice(payment.amount_cents, payment.currency)}
                    </TableCell>
                    <TableCell>
                      {payment.payment_method_type?.charAt(0).toUpperCase() + 
                       payment.payment_method_type?.slice(1) || "N/A"}
                    </TableCell>
                    <TableCell>
                      <Chip
                        color={getPaymentStatusColor(payment.status)}
                        variant="flat"
                        size="sm"
                      >
                        {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                      </Chip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>

      {/* Confirmation Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalContent>
          <ModalHeader>
            Confirm {actionType.charAt(0).toUpperCase() + actionType.slice(1)} Subscription
          </ModalHeader>
          <ModalBody>
            <p>
              Are you sure you want to {actionType} your subscription?
              {actionType === "cancel" && " This action cannot be undone."}
            </p>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose}>
              Cancel
            </Button>
            <Button
              color={actionType === "cancel" ? "danger" : "primary"}
              onPress={handleAction}
              isLoading={isLoading}
            >
              {actionType.charAt(0).toUpperCase() + actionType.slice(1)}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};
