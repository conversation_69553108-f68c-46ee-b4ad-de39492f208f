"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardBody,
  CardHeader,
  <PERSON>ton,
  Modal,
  <PERSON>dal<PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  Modal<PERSON>ooter,
  useDisclosure,
  Chip,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { StripeProvider } from "./stripe-provider";
import { PaymentForm } from "./payment-form";
import toast from "react-hot-toast";

interface PaymentMethod {
  id: string;
  type: string;
  card?: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
  };
}

interface PaymentMethodsProps {
  paymentMethods: PaymentMethod[];
  onAddPaymentMethod: () => Promise<string>; // Returns client secret
  onRemovePaymentMethod: (paymentMethodId: string) => Promise<void>;
  onRefresh: () => Promise<void>;
}

export const PaymentMethods: React.FC<PaymentMethodsProps> = ({
  paymentMethods,
  onAddPaymentMethod,
  onRemovePaymentMethod,
  onRefresh,
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [clientSecret, setClientSecret] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [removingId, setRemovingId] = useState<string>("");

  const getBrandIcon = (brand: string) => {
    switch (brand.toLowerCase()) {
      case "visa":
        return "logos:visa";
      case "mastercard":
        return "logos:mastercard";
      case "amex":
        return "logos:amex";
      case "discover":
        return "logos:discover";
      default:
        return "solar:card-outline";
    }
  };

  const handleAddPaymentMethod = async () => {
    try {
      setIsLoading(true);
      const secret = await onAddPaymentMethod();
      setClientSecret(secret);
      onOpen();
    } catch (error) {
      toast.error("Failed to setup payment method");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemovePaymentMethod = async (paymentMethodId: string) => {
    try {
      setRemovingId(paymentMethodId);
      await onRemovePaymentMethod(paymentMethodId);
      toast.success("Payment method removed");
      await onRefresh();
    } catch (error) {
      toast.error("Failed to remove payment method");
    } finally {
      setRemovingId("");
    }
  };

  const handlePaymentSuccess = async () => {
    toast.success("Payment method added successfully");
    onClose();
    setClientSecret("");
    await onRefresh();
  };

  const handlePaymentError = (error: string) => {
    toast.error(error);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <Icon icon="solar:wallet-money-outline" className="text-2xl text-primary" />
              <h2 className="text-xl font-semibold">Payment Methods</h2>
            </div>
            <Button
              color="primary"
              variant="flat"
              onPress={handleAddPaymentMethod}
              isLoading={isLoading}
              startContent={<Icon icon="solar:add-circle-outline" />}
            >
              Add Payment Method
            </Button>
          </div>
        </CardHeader>
        <CardBody>
          {paymentMethods.length === 0 ? (
            <div className="text-center py-8 text-default-500">
              <Icon icon="solar:wallet-outline" className="text-4xl mb-2" />
              <p>No payment methods found</p>
              <p className="text-sm">Add a payment method to get started</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {paymentMethods.map((method) => (
                <Card key={method.id} className="border border-default-200">
                  <CardBody>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Icon
                          icon={getBrandIcon(method.card?.brand || "")}
                          className="text-2xl"
                        />
                        <div>
                          <p className="font-medium">
                            •••• •••• •••• {method.card?.last4}
                          </p>
                          <p className="text-sm text-default-500">
                            Expires {method.card?.exp_month}/{method.card?.exp_year}
                          </p>
                          <Chip size="sm" variant="flat" color="primary">
                            {method.card?.brand.toUpperCase()}
                          </Chip>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="light"
                        color="danger"
                        isIconOnly
                        onPress={() => handleRemovePaymentMethod(method.id)}
                        isLoading={removingId === method.id}
                      >
                        <Icon icon="solar:trash-bin-minimalistic-outline" />
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          )}
        </CardBody>
      </Card>

      {/* Add Payment Method Modal */}
      <Modal 
        isOpen={isOpen} 
        onClose={onClose}
        size="lg"
        classNames={{
          body: "py-6",
          backdrop: "bg-black/50 backdrop-opacity-40",
          base: "border-default-200 bg-content1",
          header: "border-b-[1px] border-default-200",
          footer: "border-t-[1px] border-default-200",
        }}
      >
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center gap-2">
              <Icon icon="solar:card-outline" className="text-xl" />
              Add Payment Method
            </div>
          </ModalHeader>
          <ModalBody>
            {clientSecret && (
              <StripeProvider clientSecret={clientSecret}>
                <PaymentForm
                  onSuccess={handlePaymentSuccess}
                  onError={handlePaymentError}
                  submitText="Add Payment Method"
                  showCard={false}
                />
              </StripeProvider>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </div>
  );
};
