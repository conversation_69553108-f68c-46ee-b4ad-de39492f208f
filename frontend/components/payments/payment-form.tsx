"use client";

import React, { useState } from "react";
import {
  PaymentElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { <PERSON><PERSON>, Card, CardBody, CardHeader } from "@heroui/react";
import { Icon } from "@iconify/react";
import toast from "react-hot-toast";

interface PaymentFormProps {
  onSuccess?: (paymentIntent: any) => void;
  onError?: (error: string) => void;
  submitText?: string;
  showCard?: boolean;
}

export const PaymentForm: React.FC<PaymentFormProps> = ({
  onSuccess,
  onError,
  submitText = "Pay Now",
  showCard = true,
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string>("");

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setMessage("");

    const { error, paymentIntent } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${window.location.origin}/payments/success`,
      },
      redirect: "if_required",
    });

    if (error) {
      const errorMessage = error.message || "An unexpected error occurred.";
      setMessage(errorMessage);
      onError?.(errorMessage);
      toast.error(errorMessage);
    } else if (paymentIntent && paymentIntent.status === "succeeded") {
      setMessage("Payment succeeded!");
      onSuccess?.(paymentIntent);
      toast.success("Payment successful!");
    }

    setIsLoading(false);
  };

  const content = (
    <form onSubmit={handleSubmit} className="space-y-6">
      <PaymentElement
        options={{
          layout: "tabs",
        }}
      />
      
      {message && (
        <div className={`text-sm p-3 rounded-lg ${
          message.includes("succeeded") 
            ? "bg-green-50 text-green-700 border border-green-200" 
            : "bg-red-50 text-red-700 border border-red-200"
        }`}>
          {message}
        </div>
      )}

      <Button
        type="submit"
        color="primary"
        size="lg"
        fullWidth
        isLoading={isLoading}
        disabled={!stripe || !elements || isLoading}
        startContent={
          !isLoading && <Icon icon="solar:card-outline" className="text-lg" />
        }
      >
        {isLoading ? "Processing..." : submitText}
      </Button>
    </form>
  );

  if (!showCard) {
    return content;
  }

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader className="flex gap-3">
        <Icon icon="solar:card-outline" className="text-2xl text-primary" />
        <div className="flex flex-col">
          <p className="text-md font-semibold">Payment Details</p>
          <p className="text-small text-default-500">
            Enter your payment information
          </p>
        </div>
      </CardHeader>
      <CardBody>
        {content}
      </CardBody>
    </Card>
  );
};
