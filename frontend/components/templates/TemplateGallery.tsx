"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@heroui/react";

import { useEffect } from "react";
import Image from "next/image";
import { Template } from "@/types";
import { useResumeCustomization } from "@/contexts/ResumeCustomizationContext";

interface TemplateGalleryProps {
  templates: Template[];
}

export const TemplateGallery: React.FC<TemplateGalleryProps> = ({
  templates,
}) => {
  const { selectedTemplateId, setSelectedTemplateId } =
    useResumeCustomization();

  useEffect(() => {
    if (!selectedTemplateId) {
      setSelectedTemplateId(templates[0].id);
    }
  }, [selectedTemplateId, templates]);

  return (
    <>
      {/* Template List */}
      <div className="col-span-1">
        <h2 className="text-xl font-semibold mb-4">Templates</h2>
        <div className="gap-4 flex overflow-y-auto overflow-x-hidden scrollbar-hide flex-wrap max-w-full justify-center p-2">
          {templates.map((template) => (
            <Card
              key={template.id}
              isPressable
              className={`cursor-pointer transition-all w-36 shadow-md hover:shadow-lg ${
                selectedTemplateId === template.id
                  ? "border-2 border-blue-500 ring-2 ring-blue-200"
                  : "border border-gray-200 hover:border-gray-300"
              }`}
              onPress={() => {
                setSelectedTemplateId(template.id);
              }}
            >
              <CardBody className="p-0 relative overflow-hidden">
                {template.image && (
                  <div className="relative w-full h-48 group">
                    <Image
                      alt={template.name}
                      className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                      height={192}
                      src={`/assets/images/templates/${template.image}`}
                      width={300}
                    />
                    {/* Overlay with gradient background */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-80 group-hover:opacity-90 transition-opacity duration-300" />
                    {/* Template name overlay */}
                    <div className="absolute bottom-0 left-0 right-0 p-4 transform translate-y-0 group-hover:-translate-y-1 transition-transform duration-300">
                      <h3 className="font-semibold text-white text-sm drop-shadow-lg">
                        {template.name}
                      </h3>
                      <p className="text-xs text-gray-200 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        {template.category.charAt(0).toUpperCase() +
                          template.category.slice(1)}
                      </p>
                    </div>
                  </div>
                )}
              </CardBody>
              <CardFooter className="px-4 py-3 bg-gray-50/50 border-t border-gray-100">
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-2">
                    <Chip
                      className="text-xs"
                      color="success"
                      size="sm"
                      variant="flat"
                    >
                      Free
                    </Chip>
                    <Chip
                      className="text-xs"
                      color="primary"
                      size="sm"
                      variant="flat"
                    >
                      ATS {template.ats_score}/10
                    </Chip>
                  </div>
                  <Button
                    className="text-xs font-medium"
                    color="primary"
                    size="sm"
                    variant="solid"
                    onPress={() => setSelectedTemplateId(template.id)}
                  >
                    Select
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </>
  );
};
