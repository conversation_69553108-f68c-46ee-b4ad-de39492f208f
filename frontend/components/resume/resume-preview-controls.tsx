import { But<PERSON>, Toolt<PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";

interface ResumePreviewControlsProps {
  onTemplateGalleryOpen: () => void;
  handleFullScreen: () => void;
  exportPdf: () => void;
}

export const ResumePreviewControls: React.FC<ResumePreviewControlsProps> = ({
  onTemplateGalleryOpen,
  exportPdf,
  handleFullScreen,
}) => {
  return (
    <>
      <div className="preview-controls flex items-center justify-between p-4 border-b bg-gray-50">
        <div className="flex items-center gap-2">
          {/* Template Customization Button */}
          <Tooltip
            content={
              <div className="p-2">
                <p className="font-semibold mb-1">Customize Your Resume</p>
                <p className="text-sm text-gray-600">
                  Change templates, colors, fonts, and layout options
                </p>
              </div>
            }
          >
            <Button
              className="font-medium shadow-lg hover:shadow-xl transition-all duration-200 animate-pulse hover:animate-none"
              color="primary"
              size="sm"
              startContent={<Icon icon="lucide:palette" />}
              variant="flat"
              onPress={onTemplateGalleryOpen}
            >
              Customize
            </Button>
          </Tooltip>
        </div>

        <div className="flex items-center gap-2">
          {/* Zoom Controls */}
          <div className="flex items-center gap-1 border rounded-lg p-1">
            <Button
              isIconOnly
              size="sm"
              variant="light"
              onPress={handleFullScreen}
            >
              <Icon height={16} icon="lucide:fullscreen" width={16} />
            </Button>
          </div>

          {/* Action Buttons */}
          <Button
            color="primary"
            size="sm"
            startContent={<Icon icon="lucide:download" />}
            onPress={exportPdf}
          >
            Download PDF
          </Button>
        </div>
      </div>
    </>
  );
};
