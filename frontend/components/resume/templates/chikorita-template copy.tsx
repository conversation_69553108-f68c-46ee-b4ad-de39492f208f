import { createMarkup } from "@/lib/utils";
import { Resume } from "@/types/resume";
import Image from "next/image";
import React from "react";
import {
  TemplateProps,
  formatDate,
  formatDateRange,
  formatLocation,
  getFullName,
} from "./base-components";
// Helper function to get social link by platform
const getSocialLink = (resume: Resume, platform: string) => {
  return (
    resume.social_links?.find((link) =>
      link.platform.toLowerCase().includes(platform.toLowerCase()),
    )?.url || "#"
  );
};

// Sidebar section component
interface SidebarSectionProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

const SidebarSection: React.FC<SidebarSectionProps> = ({
  title,
  children,
  className = "",
}) => (
  <section className={`mb-6 ${className}`}>
    <h3 className="text-lg font-bold mb-4 border-b pb-2 !text-[--resume-background]">
      {title}
    </h3>
    {children}
  </section>
);

// Main content section component
interface SectionProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

const Section: React.FC<SectionProps> = ({
  title,
  children,
  className = "",
}) => (
  <section className={`mb-8 ${className}`}>
    <h2 className="text-lg font-bold mb-3 border-b pb-1">{title}</h2>
    {children}
  </section>
);

/**
 * Modern Resume Template
 * - Clean, professional two-column layout
 * - Green sidebar with white text
 * - Simple, readable typography
 * - ATS-friendly structure
 */
export const ChikoritaTemplate: React.FC<TemplateProps> = ({
  resume,
  className = "",
}) => {
  const fullName = getFullName(resume.first_name, resume.last_name);
  const location = formatLocation(resume.city, resume.country);

  // Extract social links
  const socialLinks = {
    linkedin: getSocialLink(resume, "linkedin"),
    github: getSocialLink(resume, "github"),
    stackoverflow: getSocialLink(resume, "stackoverflow"),
  };

  // Group skills by category
  const skillsByCategory = resume.skills?.reduce(
    (acc, skill) => {
      const category = skill.category || "Other";
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(skill);
      return acc;
    },
    {} as Record<string, Array<(typeof resume.skills)[number]>>,
  );

  // Main content column (left)
  const LeftColumn = (
    <div className="space-y-6">
      {/* Header with Photo, Name and Title */}
      <header className="mb-6">
        <div className="flex items-start gap-4">
          {resume.show_photo && resume.photo && (
            <div className="flex-shrink-0">
              <Image
                alt={fullName}
                className="w-24 h-32 object-cover border border-gray-300"
                height={128}
                src={resume.photo}
                width={96}
              />
            </div>
          )}
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-[--resume-primary]">
              {fullName}
            </h1>
            <h2 className="text-base">{resume.job_title}</h2>

            {/* Contact Information */}
            <div className="space-y-1 text-sm text-color-text">
              {location && (
                <div className="flex items-center">
                  <span className="mr-2">📍</span>
                  <span>{location}</span>
                </div>
              )}
              {resume.phone && (
                <div className="flex items-center">
                  <span className="mr-2">📞</span>
                  <span>{resume.phone}</span>
                </div>
              )}
              {resume.email && (
                <div className="flex items-center">
                  <span className="mr-2">✉️</span>
                  <span>{resume.email}</span>
                </div>
              )}
              {resume.website && (
                <div className="flex items-center">
                  <span className="mr-2">🌐</span>
                  <span>{resume.website}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Profiles Section */}
      {resume.social_links && resume.social_links.length > 0 && (
        <Section title="Profiles">
          <div className="grid grid-cols-3 gap-4 text-sm">
            {socialLinks.linkedin !== "#" && (
              <div className="flex items-center">
                <span className="mr-2 text-blue-600">💼</span>
                <div>
                  <div className="font-medium">johndoe</div>
                  <div className="text-gray-500">LinkedIn</div>
                </div>
              </div>
            )}
            {socialLinks.github !== "#" && (
              <div className="flex items-center">
                <span className="mr-2">🐙</span>
                <div>
                  <div className="font-medium">johndoe</div>
                  <div className="text-resume-primary-color">GitHub</div>
                </div>
              </div>
            )}
            {socialLinks.stackoverflow !== "#" && (
              <div className="flex items-center">
                <span className="mr-2 text-orange-500">📚</span>
                <div>
                  <div className="font-medium">johndoe</div>
                  <div className="text-gray-500">StackOverflow</div>
                </div>
              </div>
            )}
          </div>
        </Section>
      )}

      {/* Summary */}
      {resume.bio?.body && (
        <Section title="Summary">
          <div
            dangerouslySetInnerHTML={createMarkup(resume.bio.body)}
            className="text-sm text-resume-text-color prose prose-sm max-w-full"
          />
        </Section>
      )}

      {/* Experience */}
      {resume.experiences && resume.experiences.length > 0 && (
        <Section title="Experience">
          <div className="space-y-4">
            {resume.experiences.map((exp) => (
              <div key={exp.id}>
                <div className="flex justify-between items-start mb-1">
                  <div>
                    <h3 className="font-bold">{exp.company}</h3>
                    <p className="text-sm">{exp.title}</p>
                  </div>
                  <div className="text-right text-sm">
                    <div>{formatDateRange(exp.start_date, exp.end_date)}</div>
                    <div>{formatLocation(exp.city, exp.country)}</div>
                  </div>
                </div>
                {exp.website && (
                  <p className="text-xs text-blue-600 mb-2">🔗 {exp.website}</p>
                )}
                {exp.description && (
                  <div className="text-sm">
                    {exp.description.split("\n").map((line, index) => (
                      <p key={index} className="leading-relaxed">
                        {line.trim().startsWith("•") ? line : `• ${line}`}
                      </p>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </Section>
      )}

      {/* Education */}
      {resume.educations && resume.educations.length > 0 && (
        <Section title="Education">
          <div className="space-y-3">
            {resume.educations.map((edu) => (
              <div key={edu.id} className="flex justify-between">
                <div>
                  <h3 className="font-bold">{edu.institution}</h3>
                  <p className="text-sm">
                    {formatLocation(edu.city, edu.country)}
                  </p>
                </div>
                <div className="text-right text-sm">
                  <div>{formatDateRange(edu.start_date, edu.end_date)}</div>
                  <div>{edu.degree}</div>
                </div>
              </div>
            ))}
          </div>
        </Section>
      )}

      {/* Projects */}
      {resume.projects && resume.projects.length > 0 && (
        <Section title="Projects">
          <div className="grid grid-cols-2 gap-4">
            {resume.projects.map((project) => (
              <div key={project.id}>
                <h3 className="font-bold">{project.title}</h3>
                <p className="text-xs">{project.client}</p>
                {project.description && (
                  <p className="text-xs">{project.description}</p>
                )}
              </div>
            ))}
          </div>
        </Section>
      )}
    </div>
  );

  // Right Column (Sidebar)
  const RightColumn = (
    <div className="space-y-6 bg-[--resume-primary] text-[--resume-background]">
      {/* Skills */}
      {resume.skills && resume.skills.length > 0 && (
        <SidebarSection title="Skills">
          <div className="space-y-4">
            {Object.entries(skillsByCategory).map(([category, skills]) => (
              <div key={category}>
                <h4 className="font-bold !text-[--resume-background]">
                  {category}
                </h4>
                <div className="text-sm">
                  {skills.map((skill, index) => (
                    <div key={skill.id}>
                      {skill.name}
                      {index < skills.length - 1 ? ", " : ""}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </SidebarSection>
      )}

      {/* Tools */}
      <SidebarSection title="Tools">
        <div className="space-y-2">
          <h4 className="font-bold">Intermediate</h4>
          <div className="text-sm">Webpack, Git, Jenkins, Docker, JIRA</div>
        </div>
      </SidebarSection>

      {/* Certifications */}
      {resume.certifications && resume.certifications.length > 0 && (
        <SidebarSection title="Certifications">
          <div className="space-y-3">
            {resume.certifications.map((cert) => (
              <div key={cert.id}>
                <h4 className="font-bold !text-[--resume-background]">
                  {cert.title}
                </h4>
                <p className="text-sm !text-[--resume-background]">
                  {cert.issuer}
                </p>
                <p className="text-xs !text-[--resume-background]">
                  {formatDate(cert.date_recieved)}
                </p>
              </div>
            ))}
          </div>
        </SidebarSection>
      )}

      {/* Languages */}
      {resume.languages && resume.languages.length > 0 && (
        <SidebarSection title="Languages">
          <div className="space-y-2">
            {resume.languages.map((lang) => (
              <div key={lang.id}>
                <div className="font-bold !text-[--resume-background]">
                  {lang.name}
                </div>
                <div className="text-sm">
                  {lang.proficiency === 5
                    ? "Native Speaker"
                    : lang.proficiency === 4
                      ? "Fluent"
                      : lang.proficiency === 3
                        ? "Intermediate"
                        : lang.proficiency === 2
                          ? "Basic"
                          : "Beginner"}
                </div>
              </div>
            ))}
          </div>
        </SidebarSection>
      )}

      {/* References */}
      <SidebarSection title="References">
        <div className="text-sm !text-[--resume-background]">
          Available upon request
        </div>
      </SidebarSection>
    </div>
  );

  return (
    <div
      className={`chikorita-template bg-white text-gray-800 font-sans ${className}`}
    >
      <div className="max-w-6xl mx-auto">
        <main className="grid grid-cols-1 lg:grid-cols-3 gap-0 min-h-screen">
          <div className="lg:col-span-2 bg-white p-6">{LeftColumn}</div>
          <div className="lg:col-span-1 bg-[--resume-primary] p-6">
            {RightColumn}
          </div>
        </main>
      </div>
    </div>
  );
};
