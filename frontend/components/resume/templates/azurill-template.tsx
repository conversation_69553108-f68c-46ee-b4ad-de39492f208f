import React from "react";
import Image from "next/image";
import {
  TemplateProps,
  getFullName,
  formatLocation,
  formatDate,
  formatDateRange,
} from "./base-components";
import { GithubIcon } from "@/components/icons"; // Assuming you have this
import { Resume } from "@/types/resume";

const MainSection: React.FC<{
  title: string;
  children: React.ReactNode;
  className?: string;
}> = ({ title, children, className = "" }) => (
  <section className={`mb-6 ${className}`}>
    <h2 className="text-sm font-bold uppercase text-gray-500 tracking-widest pb-1 mb-4 border-b-2 border-gray-200">
      {title}
    </h2>
    {children}
  </section>
);

const SidebarSection: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <section className="mb-6">
    <h2 className="text-sm font-semibold uppercase text-gray-200 tracking-wider pb-1 mb-3">
      {title}
    </h2>
    {children}
  </section>
);

/**
 * Azurill Resume Template
 * - Left sidebar with dark background for contact info and secondary sections
 * - Main content area for primary information
 * - Clean typography with professional appearance
 * - ATS-friendly structure with proper hierarchy
 * - Inspired by the Azurill design from rxresu.me
 */
export const AzurillTemplate: React.FC<TemplateProps> = ({
  resume,
  className = "",
}) => {
  const fullName = getFullName(resume.first_name, resume.last_name);
  const location = formatLocation(resume.city, resume.country);

  const contactInfo = [
    { icon: "📍", text: location },
    { icon: "📞", text: resume.address }, // Using address for phone as sample data is limited
    { icon: "✉️", text: resume.email },
    { icon: "🌐", text: resume.website },
  ].filter((item) => item.text);

  return (
    <div
      className={`azurill-template bg-white text-gray-800 font-sans ${className} flex`}
    >
      {/* Main Content (Left) */}
      <div className="w-2/3 p-8">
        {/* Header */}
        <header className="flex items-center mb-8">
          {resume.show_photo && resume.photo && (
            <div className="mr-6 flex-shrink-0">
              <Image
                src={resume.photo}
                alt={fullName}
                width={120}
                height={120}
                className="rounded-md"
              />
            </div>
          )}
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{fullName}</h1>
            <h2 className="text-lg text-gray-600 font-medium">
              {resume.job_title}
            </h2>
            <div className="mt-3 space-y-1 text-xs text-gray-600">
              {contactInfo.map((item, i) => (
                <div key={i} className="flex items-center">
                  <span className="mr-2">{item.icon}</span>
                  <span>{item.text}</span>
                </div>
              ))}
            </div>
          </div>
        </header>

        {/* Profiles */}
        <MainSection title="Profiles">
          <div className="flex space-x-6">
            {/* This is a placeholder, adapt to your data */}
            <div className="flex items-center text-sm">
              <GithubIcon className="w-4 h-4 mr-2" />{" "}
              <span>{resume.website}</span>
            </div>
          </div>
        </MainSection>

        {/* Summary */}
        {resume.bio?.body && (
          <MainSection title="Summary">
            <div
              dangerouslySetInnerHTML={{ __html: resume.bio.body }}
              className="text-sm text-gray-700 leading-relaxed prose prose-sm max-w-none"
            />
          </MainSection>
        )}

        {/* Experience */}
        {resume.experiences && resume.experiences.length > 0 && (
          <MainSection title="Experience">
            <div className="space-y-5">
              {resume.experiences.map((exp) => (
                <div key={exp.id}>
                  <div className="flex justify-between items-baseline">
                    <h3 className="font-bold text-gray-800">{exp.company}</h3>
                    <div className="text-xs font-bold text-gray-600">
                      {formatDateRange(
                        exp.start_date,
                        exp.end_date,
                        exp.is_current
                      )}
                    </div>
                  </div>
                  <div className="flex justify-between items-baseline">
                    <p className="text-sm font-semibold text-gray-700">
                      {exp.title}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatLocation(exp.city, exp.country)}
                    </p>
                  </div>
                  {exp.description && (
                    <ul className="mt-2 text-sm text-gray-700 list-disc list-inside space-y-1">
                      {exp.description.split("\n").map((line, i) => (
                        <li key={i}>{line}</li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </MainSection>
        )}

        {/* Education */}
        {resume.educations && resume.educations.length > 0 && (
          <MainSection title="Education">
            <div className="space-y-4">
              {resume.educations.map((edu) => (
                <div key={edu.id} className="flex justify-between">
                  <div>
                    <h3 className="font-bold text-gray-800">
                      {edu.institution}
                    </h3>
                    <p className="text-sm text-gray-700">
                      {edu.degree} in {edu.field_of_study}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatLocation(edu.city, edu.country)}
                    </p>
                  </div>
                  <div className="text-right text-xs font-bold text-gray-600">
                    {formatDateRange(
                      edu.start_date,
                      edu.end_date,
                      edu.is_current
                    )}
                  </div>
                </div>
              ))}
            </div>
          </MainSection>
        )}

        {/* Projects */}
        {resume.projects && resume.projects.length > 0 && (
          <MainSection title="Projects" className="break-inside-avoid">
            <div className="grid grid-cols-2 gap-x-6 gap-y-4">
              {resume.projects.map((project) => (
                <div key={project.id}>
                  <h3 className="font-bold text-gray-800 text-sm">
                    {project.title}
                  </h3>
                  <p className="text-xs font-semibold text-gray-600 mb-1">
                    {project.client}
                  </p>
                  <p className="text-xs text-gray-700">{project.description}</p>
                </div>
              ))}
            </div>
          </MainSection>
        )}
      </div>

      {/* Sidebar (Right) */}
      <div className="w-1/3 bg-gray-700 text-white p-8">
        {resume.skills && resume.skills.length > 0 && (
          <SidebarSection title="Skills">
            <div className="space-y-4">
              {(() => {
                const skillsByCategory = resume.skills.reduce(
                  (acc, skill) => {
                    const category = skill.category || "General";
                    if (!acc[category]) acc[category] = [];
                    acc[category].push(skill);
                    return acc;
                  },
                  {} as Record<string, Resume["skills"]>
                );

                return Object.entries(skillsByCategory).map(
                  ([category, skills]) => (
                    <div key={category}>
                      <h3 className="text-sm font-bold mb-1 text-gray-100">
                        {category}
                      </h3>
                      <p className="text-xs text-gray-300">
                        {skills.map((s) => s.name).join(", ")}
                      </p>
                    </div>
                  )
                );
              })()}
            </div>
          </SidebarSection>
        )}

        {resume.certifications && resume.certifications.length > 0 && (
          <SidebarSection title="Certifications">
            <div className="space-y-3 text-sm">
              {resume.certifications.map((cert) => (
                <div key={cert.id}>
                  <h3 className="font-semibold text-gray-100">{cert.title}</h3>
                  <p className="text-xs text-gray-300">{cert.issuer}</p>
                  <p className="text-xs text-gray-400">
                    {formatDate(cert.date_obtained)}
                  </p>
                </div>
              ))}
            </div>
          </SidebarSection>
        )}

        {resume.languages && resume.languages.length > 0 && (
          <SidebarSection title="Languages">
            <div className="space-y-2 text-sm">
              {resume.languages.map((lang) => (
                <div key={lang.id} className="flex justify-between">
                  <span className="font-semibold text-gray-100">
                    {lang.name}
                  </span>
                  <span className="text-xs text-gray-300">
                    ({lang.proficiency}%)
                  </span>
                </div>
              ))}
            </div>
          </SidebarSection>
        )}

        {resume.references && resume.references.length > 0 && (
          <SidebarSection title="References">
            <p className="text-xs text-gray-300">Available upon request.</p>
          </SidebarSection>
        )}
      </div>
    </div>
  );
};

export default AzurillTemplate;
