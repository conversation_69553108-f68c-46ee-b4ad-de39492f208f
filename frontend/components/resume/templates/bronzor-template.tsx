import React from "react";
import {
  TemplateProps,
  getFullName,
  formatLocation,
  formatDate,
  formatDateRange,
} from "./base-components";

// Clean section component with simple line divider
const CleanSection: React.FC<{ title: string; children: React.ReactNode }> = ({
  title,
  children,
}) => (
  <section className="mb-6">
    <h2 className="text-lg font-bold text-gray-900 mb-3 pb-1 border-b border-gray-300">
      {title}
    </h2>
    {children}
  </section>
);

/**
 * Bronzor Resume Template
 * - Professional metallic-inspired design
 * - Single column layout with bronze accents
 * - Clean typography with subtle metallic elements
 * - ATS-friendly structure with professional appearance
 * - Inspired by bronze/metallic design elements
 */
export const BronzorTemplate: React.FC<TemplateProps> = ({
  resume,
  className = "",
}) => {
  const fullName = getFullName(resume.first_name, resume.last_name);
  const location = formatLocation(resume.city, resume.country);

  return (
    <div
      className={`bronzor-template bg-white text-gray-900 font-sans ${className}`}
    >
      <div className="max-w-4xl mx-auto p-8">
        {/* Header */}
        <header className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {fullName}
          </h1>
          <p className="text-lg text-gray-600 mb-4">
            {resume.job_title}
          </p>

          {/* Contact info with green bullets */}
          <div className="flex justify-center items-center flex-wrap gap-x-6 gap-y-2 text-sm text-gray-600 mb-6">
            {location && (
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                <span>{location}</span>
              </div>
            )}

            {resume.email && (
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                <span>{resume.email}</span>
              </div>
            )}
            {resume.website && (
              <div className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                <span>{resume.website}</span>
              </div>
            )}
          </div>
        </header>

        {/* Profiles Section */}
        <CleanSection title="Profiles">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center">
              <span className="text-blue-600 mr-2">📧</span>
              <span className="font-medium mr-2">LinkedIn</span>
              <span className="text-gray-600">johndoe</span>
            </div>
            <div className="flex items-center">
              <span className="text-gray-800 mr-2">🐙</span>
              <span className="font-medium mr-2">GitHub</span>
              <span className="text-gray-600">johndoe</span>
            </div>
            <div className="flex items-center">
              <span className="text-orange-500 mr-2">📊</span>
              <span className="font-medium mr-2">StackOverflow</span>
              <span className="text-gray-600">johndoe</span>
            </div>
          </div>
        </CleanSection>

        {/* Main content */}
        <main className="space-y-6">
          {/* Summary */}
          {resume.bio?.body && (
            <CleanSection title="Summary">
              <div
                dangerouslySetInnerHTML={{ __html: resume.bio.body }}
                className="text-gray-700 leading-relaxed"
              />
            </CleanSection>
          )}

          {/* Experience */}
          {resume.experiences && resume.experiences.length > 0 && (
            <CleanSection title="Experience">
              <div className="space-y-6">
                {resume.experiences.map((exp) => (
                  <div key={exp.id} className="mb-6">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="text-base font-bold text-gray-900">{exp.company}</h3>
                        <p className="text-gray-700 text-sm">{exp.title}</p>
                        <p className="text-gray-600 text-sm">🔗 https://{exp.company.toLowerCase().replace(/\s+/g, '')}.com/</p>
                      </div>
                      <div className="text-right text-sm text-gray-600">
                        <p className="font-medium">
                          {formatDateRange(exp.start_date, exp.end_date, exp.is_current)}
                        </p>
                        <p>{formatLocation(exp.city, exp.country)}</p>
                      </div>
                    </div>
                    {exp.description && (
                      <div className="text-gray-700 mt-3 leading-relaxed text-sm">
                        {exp.description.split("\n").map((line, index) => (
                          <div key={index} className="mb-1 flex">
                            <span className="mr-2">•</span>
                            <span>{line}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CleanSection>
          )}

          {/* Skills */}
          {resume.skills && resume.skills.length > 0 && (
            <CleanSection title="Skills">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {(() => {
                  const skillsByCategory = resume.skills.reduce(
                    (acc, skill) => {
                      const category = skill.category || "Technical Skills";
                      if (!acc[category]) acc[category] = [];
                      acc[category].push(skill);
                      return acc;
                    },
                    {} as Record<string, typeof resume.skills>,
                  );

                  return Object.entries(skillsByCategory).map(
                    ([category, categorySkills]) => (
                      <div key={category}>
                        <h4 className="font-bold text-gray-900 mb-2 text-sm">
                          {category}
                        </h4>
                        <p className="text-gray-600 text-sm">
                          {categorySkills.map((skill) => skill.name).join(", ")}
                        </p>
                      </div>
                    ),
                  );
                })()}
              </div>
            </CleanSection>
          )}

          {/* Education */}
          {resume.educations && resume.educations.length > 0 && (
            <CleanSection title="Education">
              <div className="space-y-4">
                {resume.educations.map((edu) => (
                  <div key={edu.id}>
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-bold text-gray-900">{edu.institution}</h3>
                        <p className="text-gray-700 text-sm">
                          {formatLocation(edu.city, edu.country)}
                        </p>
                      </div>
                      <div className="text-right text-sm text-gray-600">
                        <p className="font-medium">
                          {formatDateRange(edu.start_date, edu.end_date, edu.is_current)}
                        </p>
                        <p>{edu.degree} in {edu.field_of_study}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CleanSection>
          )}

          {/* Projects */}
          {resume.projects && resume.projects.length > 0 && (
            <CleanSection title="Projects">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {resume.projects.map((project) => (
                  <div key={project.id}>
                    <h3 className="font-bold text-gray-900 mb-1">{project.title}</h3>
                    <p className="text-gray-600 text-sm mb-2">Project Lead</p>
                    {project.description && (
                      <p className="text-gray-700 text-sm leading-relaxed">
                        {project.description}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </CleanSection>
          )}

          {/* Certifications */}
          {resume.certifications && resume.certifications.length > 0 && (
            <CleanSection title="Certifications">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {resume.certifications.map((cert) => (
                  <div key={cert.id}>
                    <h4 className="font-bold text-gray-900 text-sm">{cert.title}</h4>
                    <p className="text-gray-600 text-sm">{cert.issuer}</p>
                    <p className="text-gray-600 text-sm font-medium">
                      {formatDate(cert.date_obtained)}
                    </p>
                  </div>
                ))}
              </div>
            </CleanSection>
          )}

          {/* Languages */}
          {resume.languages && resume.languages.length > 0 && (
            <CleanSection title="Languages">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {resume.languages.map((lang) => (
                  <div key={lang.id} className="flex justify-between">
                    <span className="font-medium text-gray-900">{lang.name}</span>
                    <span className="text-gray-600 text-sm">
                      {lang.proficiency === 5 ? 'Native Speaker' :
                       lang.proficiency === 4 ? 'Fluent' :
                       lang.proficiency === 3 ? 'Intermediate' :
                       lang.proficiency === 2 ? 'Basic' : 'Intermediate'}
                    </span>
                  </div>
                ))}
              </div>
            </CleanSection>
          )}

          {/* References */}
          <CleanSection title="References">
            <p className="text-gray-600 text-sm">Available upon request</p>
          </CleanSection>
        </main>
      </div>
    </div>
  );
};

export default BronzorTemplate;
