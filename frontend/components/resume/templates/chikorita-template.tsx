import { createMarkup, isUrl } from "@/lib/utils";
import { Resume } from "@/types/resume";
import Image from "next/image";
import React from "react";
import {
  TemplateProps,
  formatDate,
  formatDateRange,
  formatLocation,
  getFullName,
} from "./base-components";
import { cn } from "@heroui/react";
// Helper function to get social link by platform
const getSocialLink = (resume: Resume, platform: string) => {
  return (
    resume.social_links?.find((link) =>
      link.platform.toLowerCase().includes(platform.toLowerCase()),
    )?.url || "#"
  );
};

const Link = ({ url, icon, iconOnRight, label, className }: LinkProps) => {
  if (!isUrl(url.href)) return null;

  return (
    <div className="flex items-center gap-x-1.5">
      {!iconOnRight &&
        (icon ?? <i className="ph ph-bold ph-link text-primary group-[.sidebar]:text-white" />)}
      <a
        href={url.href}
        target="_blank"
        rel="noreferrer noopener nofollow"
        className={cn("inline-block", className)}
      >
        {label ?? (url.label || url.href)}
      </a>
      {iconOnRight &&
        (icon ?? <i className="ph ph-bold ph-link text-primary group-[.sidebar]:text-white" />)}
    </div>
  );
};

type LinkedEntityProps = {
  name: string;
  url: URL | string | null;
  className?: string;
};
const LinkedEntity = ({ name, url, className }: LinkedEntityProps) => {
  if (!url) return <div className={className}>{name}</div>;

  return (
    <Link
      href={url}
      label={name}
      icon={<i className="ph ph-bold ph-globe text-primary" />}
      iconOnRight={true}
      className={className}
    />
  )
};
// Sidebar section component
interface SidebarSectionProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

const SidebarSection: React.FC<SidebarSectionProps> = ({
  title,
  children,
  className = "",
}) => (
  <section className={`mb-6 ${className}`}>
    <h3 className="text-lg font-bold mb-4 border-b pb-2 !text-[--resume-background]">
      {title}
    </h3>
    {children}
  </section>
);

// Main content section component
interface SectionProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

const Section: React.FC<SectionProps> = ({
  title,
  children,
  className = "",
}) => (
  <section className={`mb-8 ${className}`}>
    <h2 className="text-lg font-bold mb-3 border-b pb-1">{title}</h2>
    {children}
  </section>
);

/**
 * Modern Resume Template
 * - Clean, professional two-column layout
 * - Green sidebar with white text
 * - Simple, readable typography
 * - ATS-friendly structure
 */
export const ChikoritaTemplate: React.FC<TemplateProps> = ({
  resume,
  className = "",
}) => {
  const fullName = getFullName(resume.first_name, resume.last_name);
  const location = formatLocation(resume.city, resume.country);

  // Extract social links
  const socialLinks = {
    linkedin: getSocialLink(resume, "linkedin"),
    github: getSocialLink(resume, "github"),
    stackoverflow: getSocialLink(resume, "stackoverflow"),
  };

  // Group skills by category
  const skillsByCategory = resume.skills?.reduce(
    (acc, skill) => {
      const category = skill.category || "Other";
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(skill);
      return acc;
    },
    {} as Record<string, Array<(typeof resume.skills)[number]>>,
  );

  // Main content column (left)
  const LeftColumn = (
    <div className="space-y-6">
      {/* Header with Photo, Name and Title */}
      <header className="mb-6">
        <div className="flex items-start gap-4">
          {resume.show_photo && resume.photo && (
            <div className="flex-shrink-0">
              <Image
                alt={fullName}
                className="w-24 h-32 object-cover border border-gray-300"
                height={128}
                src={resume.photo}
                width={96}
              />
            </div>
          )}
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-[--resume-primary]">
              {fullName}
            </h1>
            <h2 className="text-base">{resume.job_title}</h2>

            {/* Contact Information */}
            <div className="space-y-1 text-sm text-color-text">
              {location && (
                <div className="flex items-center">
                  <span className="mr-2">📍</span>
                  <span>{location}</span>
                </div>
              )}
              {resume.phone && (
                <div className="flex items-center">
                  <span className="mr-2">📞</span>
                  <span>{resume.phone}</span>
                </div>
              )}
              {resume.email && (
                <div className="flex items-center">
                  <span className="mr-2">✉️</span>
                  <span>{resume.email}</span>
                </div>
              )}
              {resume.website && (
                <div className="flex items-center">
                  <span className="mr-2">🌐</span>
                  <span>{resume.website}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Profiles Section */}
      {resume.social_links && resume.social_links.length > 0 && (
        <Section title="Profiles">
          <div className="grid grid-cols-3 gap-4 text-sm">
            {socialLinks.linkedin !== "#" && (
              <div className="flex items-center">
                <span className="mr-2 text-blue-600">💼</span>
                <div>
                  <div className="font-medium">johndoe</div>
                  <div className="text-gray-500">LinkedIn</div>
                </div>
              </div>
            )}
            {socialLinks.github !== "#" && (
              <div className="flex items-center">
                <span className="mr-2">🐙</span>
                <div>
                  <div className="font-medium">johndoe</div>
                  <div className="text-resume-primary-color">GitHub</div>
                </div>
              </div>
            )}
            {socialLinks.stackoverflow !== "#" && (
              <div className="flex items-center">
                <span className="mr-2 text-orange-500">📚</span>
                <div>
                  <div className="font-medium">johndoe</div>
                  <div className="text-gray-500">StackOverflow</div>
                </div>
              </div>
            )}
          </div>
        </Section>
      )}

      {/* Summary */}
      {resume.bio?.body && (
        <Section title="Summary">
          <div
            dangerouslySetInnerHTML={createMarkup(resume.bio.body)}
            className="text-sm text-resume-text-color prose prose-sm max-w-full wysiwyg"
          />
        </Section>
      )}

      {/* Experience */}
      {resume.experiences && resume.experiences.length > 0 && (
        <Section title="Experience">
          <div className="space-y-2">
            {resume.experiences.map((item) => (
              <div key={item.id} className=" space-y-2">
                <div className="flex items-start justify-between group-[.sidebar]:flex-col group-[.sidebar]:items-start">
                  <div className="text-left">
                    <LinkedEntity
                      className="font-bold"
                      name={item.company}
                      url={null}
                    />
                    <div className="font-bold">{item.title}</div>
                  </div>

                  <div className="shrink-0 text-right">
                    <div className="font-bold">
                      {formatDateRange(item.start_date, item.end_date)}
                    </div>
                    <div className="text-sm">{formatLocation(item.city, item.country)}
                    </div>
                  </div>
                </div>
                <div
                  dangerouslySetInnerHTML={{ __html: item.description }}
                  className="wysiwyg group-[.sidebar]:prose-invert"
                />
              </div>
            ))}
          </div>
        </Section>
      )}

      {/* Education */}
      {resume.educations && resume.educations.length > 0 && (
        <Section title="Education">
          <div className="space-y-2">
            {resume.educations.map((edu) => (
              <div key={edu.id} className="space-y-2">
                <div key={edu.id} className="flex justify-between items-start">
                  <div className="text-left">
                    <LinkedEntity
                      name={edu.institution}
                      url={edu.website}
                      className="font-bold"
                    />
                    <div>
                      {formatLocation(edu.city, edu.country)}
                    </div>
                    <div className="text-sm">{edu.field_of_study}</div>
                  </div>
                  <div className="text-right shrink-0">
                    <div className="font-bold">{formatDateRange(edu.start_date, edu.end_date, edu.is_current)}</div>
                    <div className="text-sm">{edu.degree}</div>
                  </div>

                </div>
                <div dangerouslySetInnerHTML={{ __html: edu.description }}
                  className="wysiwyg group-[.sidebar]:prose-invert"
                />
              </div>

            ))}
          </div>
        </Section>
      )}

      {/* Projects */}
      {resume.projects && resume.projects.length > 0 && (
        <Section title="Projects">
          <div className="grid grid-cols-2 gap-4">
            {resume.projects.map((project) => (
              <div key={project.id}>
                <h3 className="font-bold">{project.title}</h3>
                <p className="text-xs">{project.client}</p>
                {project.description && (
                  <p className="text-xs">{project.description}</p>
                )}
              </div>
            ))}
          </div>
        </Section>
      )}
    </div>
  );

  // Right Column (Sidebar)
  const RightColumn = (
    <div className="space-y-6 bg-[--resume-primary] text-[--resume-background]">
      {/* Skills */}
      {resume.skills && resume.skills.length > 0 && (
        <SidebarSection title="Skills">
          <div className="space-y-4">
            {Object.entries(skillsByCategory).map(([category, skills]) => (
              <div key={category}>
                <h4 className="font-bold !text-[--resume-background]">
                  {category}
                </h4>
                <div className="text-sm">
                  {skills.map((skill, index) => (
                    <div key={skill.id}>
                      {skill.name}
                      {index < skills.length - 1 ? ", " : ""}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </SidebarSection>
      )}

      {/* Tools */}
      <SidebarSection title="Tools">
        <div className="space-y-2">
          <h4 className="font-bold">Intermediate</h4>
          <div className="text-sm">Webpack, Git, Jenkins, Docker, JIRA</div>
        </div>
      </SidebarSection>

      {/* Certifications */}
      {resume.certifications && resume.certifications.length > 0 && (
        <SidebarSection title="Certifications">
          <div className="space-y-2">
            {resume.certifications.map((cert) => (
              <div key={cert.id} className="flex justify-between items-center">
                <div className="text-left">
                  <div className="font-bold !text-[--resume-background]">
                    {cert.title}
                  </div>
                  <div className="text-sm !text-[--resume-background]">
                    {cert.issuer}
                  </div>
                  <div className="text-xs font-bold !text-[--resume-background]">
                    {formatDate(cert.date_received)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </SidebarSection>
      )}

      {/* Languages */}
      {resume.languages && resume.languages.length > 0 && (
        <SidebarSection title="Languages">
          <div className="space-y-2">
            {resume.languages.map((lang) => (
              <div key={lang.id}>
                <div className="font-bold !text-[--resume-background]">
                  {lang.name}
                </div>
                <div className="text-sm">
                  {lang.proficiency === 5
                    ? "Native Speaker"
                    : lang.proficiency === 4
                      ? "Fluent"
                      : lang.proficiency === 3
                        ? "Intermediate"
                        : lang.proficiency === 2
                          ? "Basic"
                          : "Beginner"}
                </div>
              </div>
            ))}
          </div>
        </SidebarSection>
      )}

      {/* References */}
      <SidebarSection title="References">
        <div className="text-sm !text-[--resume-background]">
          Available upon request
        </div>
      </SidebarSection>
    </div>
  );

  return (
    <div
      className={`chikorita-template bg-white text-gray-800 font-sans ${className}`}
    >
      <div className="max-w-6xl mx-auto">
        <main className="grid grid-cols-1 lg:grid-cols-3 gap-0 min-h-screen">
          <div className="lg:col-span-2 bg-white p-4">{LeftColumn}</div>
          <div className="lg:col-span-1 bg-[--resume-primary] p-4">
            {RightColumn}
          </div>
        </main>
      </div>
    </div>
  );
};
