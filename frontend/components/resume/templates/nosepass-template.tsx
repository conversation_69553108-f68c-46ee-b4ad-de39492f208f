import Image from "next/image";
import React from "react";
import {
  TemplateProps,
  formatDate,
  formatDateRange,
  formatLocation,
  getFullName,
} from "./base-components";

const CompassSection: React.FC<{
  title: string;
  children: React.ReactNode;
  direction?: "north" | "south" | "east" | "west";
}> = ({ title, children, direction = "north" }) => {
  const directionStyles = {
    north:
      "border-t-4 border-blue-600 bg-gradient-to-b from-blue-50 to-slate-50",
    south: "border-b-4 border-red-600 bg-gradient-to-t from-red-50 to-slate-50",
    east: "border-r-4 border-green-600 bg-gradient-to-l from-green-50 to-slate-50",
    west: "border-l-4 border-yellow-600 bg-gradient-to-r from-yellow-50 to-slate-50",
  };

  const directionIcons = {
    north: "↑",
    south: "↓",
    east: "→",
    west: "←",
  };

  const directionColors = {
    north: "text-blue-600",
    south: "text-red-600",
    east: "text-green-600",
    west: "text-yellow-600",
  };

  return (
    <section className="mb-8">
      <div className="relative">
        {/* Compass point indicator */}
        <div className="absolute -left-3 top-0 w-6 h-6 bg-slate-700 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-md">
          <span className={directionColors[direction]}>
            {directionIcons[direction]}
          </span>
        </div>
        <h2 className="text-xl font-bold text-slate-800 mb-4 pl-8 flex items-center">
          <span className={`${directionColors[direction]} mr-2`}>
            {directionIcons[direction]}
          </span>
          {title}
        </h2>
      </div>
      <div
        className={`p-6 rounded-lg ${directionStyles[direction]} shadow-md border border-slate-200`}
      >
        {children}
      </div>
    </section>
  );
};

// Navigation card component
const NavigationCard: React.FC<{
  children: React.ReactNode;
  magnetic?: boolean;
  className?: string;
}> = ({ children, magnetic = false, className = "" }) => (
  <div
    className={`bg-white/90 backdrop-blur-sm p-4 rounded-lg shadow-sm border-2 ${magnetic ? "border-slate-400 shadow-lg" : "border-slate-200"} hover:shadow-md transition-all ${className}`}
  >
    {magnetic && (
      <div className="absolute top-2 right-2 w-3 h-3 bg-red-500 rounded-full shadow-sm" />
    )}
    {children}
  </div>
);

/**
 * Nosepass Resume Template
 * - Directional, compass-inspired layout with strong navigation elements
 * - Blue/slate color scheme representing reliability and direction
 * - Structured sections with clear directional flow
 * - ATS-friendly structure with navigation themes
 * - Inspired by guidance, direction, and magnetic attraction
 */
export const NosepassTemplate: React.FC<TemplateProps> = ({
  resume,
  className = "",
}) => {
  const fullName = getFullName(resume.first_name, resume.last_name);
  const location = formatLocation(resume.city, resume.country);

  return (
    <div
      className={`nosepass-template bg-gradient-to-br from-slate-50 via-blue-50 to-gray-50 text-slate-800 font-sans ${className}`}
    >
      <div className="max-w-6xl mx-auto p-8">
        {/* Header with compass-like styling */}
        <header className="text-center mb-12 relative">
          {/* Compass background */}
          <div className="absolute inset-0 bg-gradient-to-br from-slate-200/30 via-blue-200/30 to-gray-200/30 rounded-full blur-3xl" />

          <div className="relative bg-white/90 backdrop-blur-sm p-8 rounded-2xl shadow-xl border-2 border-slate-300">
            {/* Compass rose decoration */}
            <div className="absolute top-4 right-4 w-8 h-8 bg-slate-700 rounded-full flex items-center justify-center">
              <div className="w-4 h-4 bg-red-500 rounded-full relative">
                <div className="absolute inset-0 bg-gradient-to-br from-red-400 to-red-600 rounded-full" />
              </div>
            </div>

            {resume.show_photo && resume.photo && (
              <div className="mb-6">
                <div className="relative inline-block">
                  {/* Magnetic field effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-slate-500 rounded-full blur-sm opacity-40" />
                  <div className="absolute inset-0 bg-gradient-to-br from-slate-400 to-blue-500 rounded-full blur-lg opacity-20" />
                  <Image
                    alt={fullName}
                    className="relative rounded-full border-4 border-white shadow-xl"
                    height={120}
                    src={resume.photo}
                    width={120}
                  />
                </div>
              </div>
            )}

            <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-700 via-blue-700 to-slate-700 bg-clip-text text-transparent mb-2">
              {fullName}
            </h1>
            <h2 className="text-xl text-slate-600 font-medium mb-6">
              {resume.job_title}
            </h2>

            {/* Contact info with directional indicators */}
            <div className="flex justify-center items-center flex-wrap gap-6 text-sm text-slate-600">
              {location && (
                <div className="flex items-center">
                  <span className="text-blue-600 mr-1">📍</span>
                  <span>{location}</span>
                </div>
              )}
              {resume.email && (
                <div className="flex items-center">
                  <span className="text-green-600 mr-1">✉️</span>
                  <span>{resume.email}</span>
                </div>
              )}
              {resume.website && (
                <div className="flex items-center">
                  <span className="text-yellow-600 mr-1">🌐</span>
                  <span>{resume.website}</span>
                </div>
              )}
            </div>
          </div>
        </header>

        {/* Main Content with Directional Flow */}
        <main className="space-y-8">
          {/* Professional Summary - North Direction */}
          {resume.bio?.body && (
            <CompassSection direction="north" title="Professional Direction">
              <NavigationCard magnetic>
                <div
                  dangerouslySetInnerHTML={{ __html: resume.bio.body }}
                  className="leading-relaxed text-slate-700"
                />
              </NavigationCard>
            </CompassSection>
          )}

          {/* Experience - East Direction (Forward Progress) */}
          {resume.experiences && resume.experiences.length > 0 && (
            <CompassSection direction="east" title="Career Navigation">
              <div className="space-y-6">
                {resume.experiences.map((exp, index) => (
                  <NavigationCard key={exp.id} magnetic={index === 0}>
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="text-lg font-bold text-slate-800">
                          {exp.title}
                        </h3>
                        <p className="font-semibold text-blue-700">
                          {exp.company}
                        </p>
                      </div>
                      <div className="text-right text-sm text-slate-600">
                        <p>{formatLocation(exp.city, exp.country)}</p>
                        <p className="font-medium">
                          {formatDateRange(
                            exp.start_date,
                            exp.end_date,
                            exp.is_current,
                          )}
                        </p>
                      </div>
                    </div>
                    {exp.description && (
                      <div className="text-slate-700 leading-relaxed">
                        {exp.description.split("\n").map((line, index) => (
                          <p key={index} className="mb-2">
                            {line}
                          </p>
                        ))}
                      </div>
                    )}
                  </NavigationCard>
                ))}
              </div>
            </CompassSection>
          )}

          {/* Education - West Direction (Foundation) */}
          {resume.educations && resume.educations.length > 0 && (
            <CompassSection direction="west" title="Educational Foundation">
              <div className="space-y-4">
                {resume.educations.map((edu) => (
                  <NavigationCard key={edu.id}>
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-bold text-slate-800">
                          {edu.institution}
                        </h3>
                        <p className="font-medium text-yellow-700">
                          {edu.degree} in {edu.field_of_study}
                        </p>
                        <p className="text-slate-600 text-sm">
                          {formatLocation(edu.city, edu.country)}
                        </p>
                      </div>
                      <p className="text-sm font-medium text-slate-600">
                        {formatDateRange(
                          edu.start_date,
                          edu.end_date,
                          edu.is_current,
                        )}
                      </p>
                    </div>
                  </NavigationCard>
                ))}
              </div>
            </CompassSection>
          )}
          {/* Skills - South Direction (Grounding) */}
          {resume.skills && resume.skills.length > 0 && (
            <CompassSection direction="south" title="Technical Compass">
              <div className="space-y-6">
                {(() => {
                  const skillsByCategory = resume.skills.reduce(
                    (acc, skill) => {
                      const category = skill.category || "Technical";
                      if (!acc[category]) acc[category] = [];
                      acc[category].push(skill);
                      return acc;
                    },
                    {} as Record<string, typeof resume.skills>,
                  );

                  return Object.entries(skillsByCategory).map(
                    ([category, categorySkills]) => (
                      <NavigationCard key={category}>
                        <h4 className="font-semibold text-red-700 mb-3 flex items-center">
                          <span className="text-red-600 mr-2">↓</span>
                          {category}
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {categorySkills.map((skill) => (
                            <span
                              key={skill.id}
                              className="px-3 py-1 bg-gradient-to-r from-red-100 to-slate-100 text-red-800 text-sm rounded-full border border-red-200 shadow-sm"
                            >
                              {skill.name}
                            </span>
                          ))}
                        </div>
                      </NavigationCard>
                    ),
                  );
                })()}
              </div>
            </CompassSection>
          )}

          {/* Projects - Multi-directional */}
          {resume.projects && resume.projects.length > 0 && (
            <div className="bg-white/90 backdrop-blur-sm p-8 rounded-2xl shadow-xl border-2 border-slate-300">
              <h2 className="text-xl font-bold text-slate-800 mb-6 text-center flex items-center justify-center">
                <span className="w-6 h-6 bg-slate-700 rounded-full flex items-center justify-center text-white text-xs mr-3">
                  ⊕
                </span>
                Project Portfolio
              </h2>
              <div className="grid gap-4 md:grid-cols-2">
                {resume.projects.map((project, index) => {
                  const directions = [
                    "north",
                    "south",
                    "east",
                    "west",
                  ] as const;
                  const direction = directions[index % 4];
                  return (
                    <NavigationCard
                      key={project.id}
                      className={`border-l-4 ${
                        direction === "north"
                          ? "border-blue-500"
                          : direction === "south"
                            ? "border-red-500"
                            : direction === "east"
                              ? "border-green-500"
                              : "border-yellow-500"
                      }`}
                    >
                      <h3 className="font-bold text-slate-800 mb-1">
                        {project.title}
                      </h3>
                      <p className="text-slate-600 text-sm mb-2">
                        {project.client}
                      </p>
                      {project.description && (
                        <p className="text-slate-700 text-sm leading-relaxed">
                          {project.description}
                        </p>
                      )}
                    </NavigationCard>
                  );
                })}
              </div>
            </div>
          )}

          {/* Additional Sections - Compass Points */}
          <div className="grid gap-8 md:grid-cols-2">
            {/* Certifications */}
            {resume.certifications && resume.certifications.length > 0 && (
              <div className="bg-white/90 backdrop-blur-sm p-6 rounded-2xl shadow-lg border-2 border-blue-200">
                <h3 className="font-bold text-slate-800 mb-4 flex items-center">
                  <span className="text-blue-600 mr-2">↑</span>
                  Certifications
                </h3>
                <div className="space-y-3">
                  {resume.certifications.map((cert) => (
                    <div
                      key={cert.id}
                      className="border-l-3 border-blue-400 pl-3 bg-blue-50/50 p-2 rounded"
                    >
                      <h4 className="font-semibold text-slate-800 text-sm">
                        {cert.title}
                      </h4>
                      <p className="text-xs text-slate-600">{cert.issuer}</p>
                      <p className="text-xs text-blue-700">
                        {formatDate(cert.date_obtained)}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Languages */}
            {resume.languages && resume.languages.length > 0 && (
              <div className="bg-white/90 backdrop-blur-sm p-6 rounded-2xl shadow-lg border-2 border-green-200">
                <h3 className="font-bold text-slate-800 mb-4 flex items-center">
                  <span className="text-green-600 mr-2">→</span>
                  Languages
                </h3>
                <div className="space-y-3">
                  {resume.languages.map((lang) => (
                    <div
                      key={lang.id}
                      className="flex justify-between items-center bg-green-50/50 p-2 rounded"
                    >
                      <span className="text-sm text-slate-700">
                        {lang.name}
                      </span>
                      <div className="flex space-x-1">
                        {[...Array(5)].map((_, i) => (
                          <div
                            key={i}
                            className={`w-2 h-2 rounded-full ${
                              i < lang.proficiency
                                ? "bg-gradient-to-r from-green-500 to-blue-500"
                                : "bg-gray-200"
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default NosepassTemplate;
