"use client";
import React, { useMemo } from "react";

import AzurillTemplate from "./azurill-template";
import BronzorTemplate from "./bronzor-template";
import { ChikoritaTemplate } from "./chikorita-template";
import DittoTemplate from "./ditto-template";
import GengarTemplate from "./gengar-template";
import GlalieTemplate from "./glalie-template";
import KakunaTemplate from "./kakuna-template";
import LeafishTemplate from "./leafish-template";
import NosepassTemplate from "./nosepass-template";
import OnyxTemplate from "./onyx-template";
import PikachuTemplate from "./pikachu-template";
import RhyhornTemplate from "./rhyhorn-template";

import { useResumeCustomization } from "@/contexts/ResumeCustomizationContext";
import { Template } from "@/types";
import { Resume } from "@/types/resume";
interface TemplateRendererProps {
  resume: Resume;
  className?: string;
  templates: Template[];
}

// Template renderer component
export const TemplateRenderer = React.forwardRef<
  HTMLDivElement,
  TemplateRendererProps
>(({ resume, className, templates }) => {
  const { selectedTemplateId } = useResumeCustomization();
  const template = useMemo(() => {
    return (
      templates.find((template) => template.id === selectedTemplateId) ||
      templates[0]
    );
  }, [selectedTemplateId, templates]);

  if (!template) {
    return <div className="error">No templates available</div>;
  }

  const TemplateComponent = () => {
    switch (template.slug) {
      case "azurill":
        return <AzurillTemplate className={className} resume={resume} />;
      case "bronzor":
        return <BronzorTemplate className={className} resume={resume} />;
      case "chikorita":
        return <ChikoritaTemplate className={className} resume={resume} />;
      case "ditto":
        return <DittoTemplate className={className} resume={resume} />;
      case "gengar":
        return <GengarTemplate className={className} resume={resume} />;
      case "glalie":
        return <GlalieTemplate className={className} resume={resume} />;
      case "kakuna":
        return <KakunaTemplate className={className} resume={resume} />;
      case "leafish":
        return <LeafishTemplate className={className} resume={resume} />;
      case "nosepass":
        return <NosepassTemplate className={className} resume={resume} />;
      case "onyx":
        return <OnyxTemplate className={className} resume={resume} />;
      case "pikachu":
        return <PikachuTemplate className={className} resume={resume} />;
      case "rhyhorn":
        return <RhyhornTemplate className={className} resume={resume} />;
      default:
        return <BronzorTemplate className={className} resume={resume} />;
    }
  };
  return (
    <div id="resume-preview">
      <TemplateComponent />
    </div>
  );
});

TemplateRenderer.displayName = "TemplateRenderer";

// ATS compatibility indicator
export const ATSCompatibilityIndicator: React.FC<{
  score: number;
  showDetails?: boolean;
}> = ({ score, showDetails = false }) => {
  const getScoreColor = (score: number): string => {
    if (score >= 9) return "text-green-600 bg-green-100";
    if (score >= 7) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  const getScoreLabel = (score: number): string => {
    if (score >= 9) return "Excellent";
    if (score >= 7) return "Good";
    return "Fair";
  };

  return (
    <div className="ats-indicator">
      <div
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(score)}`}
      >
        ATS Score: {score}/10 ({getScoreLabel(score)})
      </div>
      {showDetails && (
        <div className="mt-2 text-xs text-gray-600">
          <p>
            ATS (Applicant Tracking System) compatibility measures how well
            resume parsing systems can read your resume.
          </p>
          <ul className="mt-1 list-disc list-inside">
            <li>9-10: Excellent - Maximum compatibility</li>
            <li>7-8: Good - Minor formatting may be lost</li>
            <li>5-6: Fair - Some content may be missed</li>
            <li>Below 5: Poor - Significant parsing issues</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default TemplateRenderer;
