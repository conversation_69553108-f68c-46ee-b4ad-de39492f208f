import { Icon } from "@iconify/react";
import { Spacer } from "@heroui/react";
import { ColorScheme } from "@/types/resume";
import { upperCaseFirstLetter } from "@/lib/utils";
import { ColorThemeInput } from "./helpers";
import { TemplateGallery } from "../templates/TemplateGallery";
import {
  COLOR_SCHEMES,
  FONT_FAMILIES,
  LAYOUT_OPTIONS,
  ResumeCustomization,
} from "@/contexts/ResumeCustomizationContext";
import { Template } from "@/types";

interface ResumeEditSettingsProps {
  templates: Template[];
  colorScheme: ColorScheme;
  fontFamily: (typeof FONT_FAMILIES)[keyof typeof FONT_FAMILIES];
  spacing: (typeof LAYOUT_OPTIONS.spacing)[keyof typeof LAYOUT_OPTIONS.spacing];
  margins: (typeof LAYOUT_OPTIONS.margins)[keyof typeof LAYOUT_OPTIONS.margins];
  customTextColor?: string;
  customBackgroundColor?: string;
  customPrimaryColor?: string;
  updateCustomization: (customization: Partial<ResumeCustomization>) => void;
}

export const ResumeEditSettings = ({
  templates,
  colorScheme,
  fontFamily,
  spacing,
  margins,
  customTextColor,
  customBackgroundColor,
  customPrimaryColor,
  updateCustomization,
}: ResumeEditSettingsProps) => {
  return (
    <>
      <div className="relative flex h-full w-72 flex-1 flex-col p-4 w-full">
        <div className="flex flex-col gap-3 px-2">
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary">
              <Icon className="text-white" icon="lucide:palette" />
            </div>
            <span className="text-lg font-bold text-foreground">
              Customize Resume
            </span>
          </div>
          <p className="text-sm text-gray-600 leading-relaxed">
            Choose templates, colors, fonts, and styling options to make your
            resume stand out.
          </p>
        </div>
        <Spacer y={6} />

        <div className="overflow-y-auto scrollbar-hide overflow-x-hidden h-full max-h-screen">
          {/* Customization Sections */}
          <div className="space-y-6">
            {/* Templates Section */}
            <div className="bg-gray-50 rounded-lg p-4">
              <TemplateGallery templates={templates} />
            </div>

            {/* Colors Section */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <Icon className="text-purple-600" icon="lucide:palette" />
                <h3 className="font-semibold text-gray-900">Colors</h3>
              </div>
              <p className="text-xs text-gray-600 mb-3">
                Choose a color scheme for your resume
              </p>
              <div className="flex flex-col gap-4">
                <div className="flex gap-4 items-center">
                  {Object.entries(COLOR_SCHEMES).map(([key, scheme]) => (
                    <button
                      key={key}
                      className={`w-5 h-5 p-2 rounded-full border-2 transition-all cursor-pointer ${colorScheme.id === key
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-200 hover:border-gray-300"
                        }`}
                      style={{ backgroundColor: scheme.primary }}
                      title={scheme.name}
                      onClick={() =>
                        updateCustomization({
                          colorScheme:
                            COLOR_SCHEMES[key as keyof typeof COLOR_SCHEMES],
                        })
                      }
                    />
                  ))}
                </div>
                <div className="flex flex-col gap-4">
                  {["primary", "background", "text"].map((key) => {
                    const value = key === "primary"
                      ? customPrimaryColor
                      : key === "text"
                        ? customTextColor
                        : customBackgroundColor
                        || (colorScheme as any)[key];
                    return <ColorThemeInput
                      key={key}
                      label={upperCaseFirstLetter(key) + " Color"}
                      value={value}
                      onChange={(value) =>
                        updateCustomization({
                          [`custom${upperCaseFirstLetter(key)}Color`]: value,
                        })
                      }
                    />
                  })}
                </div>
              </div>
            </div>

            {/* Typography Section */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <Icon className="text-green-600" icon="lucide:type" />
                <h3 className="font-semibold text-gray-900">Typography</h3>
              </div>
              <p className="text-xs text-gray-600 mb-3">
                Choose fonts and text styling options
              </p>
              <div className="space-y-2">
                {Object.entries(FONT_FAMILIES).map(([key, font]) => (
                  <button
                    key={key}
                    className={`w-full text-left p-2 rounded-lg border-2 transition-all ${fontFamily.id === key
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-200 hover:border-gray-300"
                      }`}
                    onClick={() =>
                      updateCustomization({
                        fontFamily: key as keyof typeof FONT_FAMILIES,
                      })
                    }
                  >
                    <div className="flex items-center justify-between">
                      <span
                        className="text-sm font-medium"
                        style={{ fontFamily: font.family }}
                      >
                        {font.name}
                      </span>
                      <span className="text-xs text-gray-500 capitalize">
                        {font.category}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Layout Section */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <Icon className="text-indigo-600" icon="lucide:layout" />
                <h3 className="font-semibold text-gray-900">Layout</h3>
              </div>
              <p className="text-xs text-gray-600 mb-3">
                Adjust spacing and margins
              </p>
              <div className="space-y-4">
                {/* Spacing Options */}
                <div>
                  <div className="text-xs font-medium text-gray-700 mb-2">
                    Spacing
                  </div>
                  <div className="grid grid-cols-3 gap-1">
                    {Object.entries(LAYOUT_OPTIONS.spacing).map(
                      ([key, option]) => (
                        <button
                          key={key}
                          className={`p-2 text-xs rounded border-2 transition-all ${spacing.id === key
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:border-gray-300"
                            }`}
                          onClick={() =>
                            updateCustomization({
                              spacing:
                                key as keyof typeof LAYOUT_OPTIONS.spacing,
                            })
                          }
                        >
                          {option.name}
                        </button>
                      )
                    )}
                  </div>
                </div>

                {/* Margin Options */}
                <div>
                  <div className="text-xs font-medium text-gray-700 mb-2">
                    Margins
                  </div>
                  <div className="grid grid-cols-3 gap-1">
                    {Object.entries(LAYOUT_OPTIONS.margins).map(
                      ([key, option]) => (
                        <button
                          key={key}
                          className={`p-2 text-xs rounded border-2 transition-all ${margins.id === key
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:border-gray-300"
                            }`}
                          onClick={() =>
                            updateCustomization({
                              margins:
                                key as keyof typeof LAYOUT_OPTIONS.margins,
                            })
                          }
                        >
                          {option.name}
                        </button>
                      )
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Spacer y={8} />
      </div>
    </>
  );
};
