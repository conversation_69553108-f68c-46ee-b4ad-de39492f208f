"use client";
import { Accordion, AccordionItem, Button, Form, Input } from "@heroui/react";
import { useActionState, useMemo, useEffect } from "react";

import { Icon } from "@iconify/react";
import { useRouter } from "next/navigation";
import AccordionFormList from "../AccordionFormList";
import SubmitButton from "../submit-button";

import PersonalFormFields from "./personalFormFields";

import { editResume } from "@/actions/resume";
import { useResumeCustomization } from "@/contexts/ResumeCustomizationContext";
import { useActionErrors } from "@/hooks/use-action-errors";
import { prepareNestedForms, RenderForm } from "@/lib/form";
import { upperCaseFirstLetter } from "@/lib/utils";
import { Resume } from "@/types/resume";

export const ResumeEditForm = ({ data: initialData }: { data: Resume }) => {
  const [state, action] = useActionState(editResume, {
    data: initialData,
    errors: {
      fieldErrors: {},
      formErrors: [],
    },
  });

  const {
    selectedTemplateId,
    colorScheme,
    fontFamily,
    spacing,
    margins,
    customTextColor,
    customBackgroundColor,
    customPrimaryColor,
  } = useResumeCustomization()

  console.log(customTextColor, 'custom text color')

  const router = useRouter();

  const { errors, setFieldError } = useActionErrors(state);

  const fieldErrors = errors?.fieldErrors || {};
  const formErrors = errors?.formErrors;

  const resumeData = state?.data || initialData;

  console.log(resumeData.template_id, selectedTemplateId);

  const nested_forms = useMemo(
    () => prepareNestedForms(resumeData),
    [resumeData],
  );

  return (
    <div className="flex flex-col gap-4 w-full h-full">
      <Form action={action}>
        <Input
          defaultValue={resumeData?.id.toString()}
          name="id"
          type="hidden"
        />
        <Input
          name="template_id"
          type="hidden"
          value={selectedTemplateId?.toString()}
        />
        {/* Add hidden inputs for customization data */}
        <Input
          name="resume[color_scheme]"
          type="hidden"
          value={typeof colorScheme === 'string' ? colorScheme : colorScheme.id}
        />
        <Input
          name="resume[font_family]"
          type="hidden"
          value={typeof fontFamily === 'string' ? fontFamily : fontFamily.id}
        />
        <Input
          name="resume[spacing]"
          type="hidden"
          value={typeof spacing === 'string' ? spacing : spacing.id}
        />
        <Input
          name="resume[margins]"
          type="hidden"
          value={typeof margins === 'string' ? margins : margins.id}
        />
        <Input
          name="resume[custom_primary_color]"
          type="hidden"
          value={customPrimaryColor}
        />
        <Input name="resume[custom_text_color]" type="hidden" value={customTextColor} />
        <Input name="resume[custom_background_color]" type="hidden" value={customBackgroundColor} />
        <PersonalFormFields data={resumeData} />
        <div className="w-full mt-4">
          {nested_forms.map((form) => {
            return (
              <div key={form.name} className="mb-4">
                <Accordion isCompact variant="splitted">
                  <AccordionItem
                    key={form.name}
                    aria-label={form.schema.collection}
                    indicator={addItemButton({
                      onAddNew: () => router.push(form.route(resumeData.id)),
                    })}
                    subtitle={form.schema.description}
                    title={upperCaseFirstLetter(form.schema.collection)}
                  >
                    <AccordionFormList
                      className="p-4"
                      items={form.items
                        ?.filter((item) => item.id != null)
                        .map((item, index) => ({
                          ...item,
                          id: item.id!,
                          sort: index,
                        }))}
                      keyName="id"
                      renderForm={(item, index) => (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <RenderForm
                            index={index}
                            item={item}
                            schema={form.schema}
                          />
                        </div>
                      )}
                      titlePrefix={form.schema.entity}
                    />
                  </AccordionItem>
                </Accordion>
              </div>
            );
          })}
        </div>
        <div className="my-4 w-full">
          <SubmitButton>Save</SubmitButton>
        </div>
      </Form>
    </div>
  );
};

const addItemButton = ({ onAddNew }: { onAddNew: () => void }) => {
  return (
    <Button
      isIconOnly
      color="primary"
      size="sm"
      startContent={<Icon icon="lucide:plus" />}
      variant="flat"
      onPress={onAddNew}
    />
  );
};
