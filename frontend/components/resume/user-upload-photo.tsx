import { <PERSON><PERSON>, But<PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import React from "react";
import { getInitials } from "@/lib/utils";

const MAX_FILE_SIZE = 4 * 1024 * 1024; // 4MB

export const UserPhotoUpload: React.FC<{ photo: string | null }> = ({
  photo,
}) => {
  const [image, setImage] = React.useState<string | null>(photo);
  const [isDeleted, setIsDeleted] = React.useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > MAX_FILE_SIZE) {
        alert(
          `File is too large. Max size is ${MAX_FILE_SIZE / 1024 / 1024}MB.`,
        );
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
        return;
      }
      setIsDeleted(false);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDelete = () => {
    setImage(null);
    setIsDeleted(true);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="flex flex-col items-center gap-4">
      {isDeleted && <input name="delete_photo" type="hidden" value="true" />}
      {image ? (
        <Avatar className="w-24 h-24" src={image} />
      ) : (
        <Avatar className="w-24 h-24" name={getInitials()} />
      )}
      <div className="flex gap-4">
        <Button
          as="label"
          color="primary"
          htmlFor="photo"
          isIconOnly
          title="Upload photo"
          size="sm"
          startContent={<Icon icon="lucide:upload" />}
        >
          <input
            ref={fileInputRef}
            accept="image/*"
            className="hidden"
            id="photo"
            name="photo"
            type="file"
            onChange={handleFileChange}
          />
        </Button>
        <Button
          color="danger"
          isDisabled={!image}
          size="sm"
          title="Delete photo"
          isIconOnly
          startContent={<Icon icon="lucide:trash-2" />}
          variant="flat"
          onPress={handleDelete}
        >
        </Button>
      </div>
    </div>
  );
};
