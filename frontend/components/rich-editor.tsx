import { Button, cn } from "@heroui/react";
import { Icon } from "@iconify/react";
import Color from "@tiptap/extension-color";
import ListItem from "@tiptap/extension-list-item";
import Placeholder from "@tiptap/extension-placeholder";
import TextStyle from "@tiptap/extension-text-style";
import { Editor, EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { useEffect, useState } from "react";
interface RichEditorProps {
  value: string;
  placeholder?: string;
  className?: string;
  minHeight?: string;
  readOnly?: boolean;
  id?: string;
  name?: string;
}

export function RichEditor({
  value,
  name,
  placeholder = "Write something...",
  className,
  minHeight = "200px",
  readOnly = false,
  id,
}: RichEditorProps) {
  const [content, setContent] = useState(value);

  const editor = useEditor({
    extensions: [
      Color.configure({ types: [TextStyle.name, ListItem.name] }),
      TextStyle,
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        bulletList: {
          keepMarks: true,
          keepAttributes: false, // TODO : Making this as `false` becase marks are not preserved when I try to preserve attrs, awaiting a bit of help
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false, // TODO : Making this as `false` becase marks are not preserved when I try to preserve attrs, awaiting a bit of help
        },
      }),

      Placeholder.configure({
        placeholder,
      }),
    ],
    editorProps: {
      attributes: {
        class: "prose prose-sm max-w-none h-full min-h-[200px] p-2",
      },
    },
    content: value,
    editable: !readOnly,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      setContent(html);
    },
  });

  // Update editor content when value prop changes
  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value || "");
    }
  }, [value, editor]);

  if (!editor) {
    return null;
  }

  return (
    <div
      className={cn("rounded-md border border-input bg-background", className)}
    >
      <div className="p-4 focus:outline-none" style={{ minHeight }}>
        <MenuBar editor={editor} />
        <EditorContent className="tiptop" editor={editor} id={id} />

        {/* Hidden input to include content in form submission */}
        <input name={name} type="hidden" value={content || ""} />
      </div>
    </div>
  );
}

const MenuBar = ({ editor }: { editor: Editor }) => {
  return (
    <div className="flex flex-wrap items-center gap-1 border-b border-input bg-muted p-1">
      <Button
        className={editor.isActive("bold") ? "bg-accent" : ""}
        size="sm"
        title="Bold"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleBold().run()}
      >
        <Icon height={16} icon="mdi:format-bold" width={16} />
      </Button>
      <Button
        className={editor.isActive("paragraph") ? "is-active" : ""}
        size="sm"
        title="Paragraph"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().setParagraph().run()}
      >
        <Icon height={16} icon="mdi:format-paragraph" width={16} />
      </Button>
      <Button
        className={editor.isActive("heading", { level: 1 }) ? "is-active" : ""}
        size="sm"
        title="Heading 1"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
      >
        <Icon height={16} icon="mdi:format-header-1" width={16} />
      </Button>
      <Button
        className={editor.isActive("heading", { level: 2 }) ? "is-active" : ""}
        size="sm"
        title="Heading 2"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
      >
        <Icon height={16} icon="mdi:format-header-2" width={16} />
      </Button>
      <Button
        className={editor.isActive("heading", { level: 3 }) ? "is-active" : ""}
        size="sm"
        title="Heading 3"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
      >
        <Icon height={16} icon="mdi:format-header-3" width={16} />
      </Button>
      <Button
        className={editor.isActive("heading", { level: 4 }) ? "is-active" : ""}
        size="sm"
        title="Heading 4"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleHeading({ level: 4 }).run()}
      >
        <Icon height={16} icon="mdi:format-header-4" width={16} />
      </Button>
      <Button
        className={editor.isActive("heading", { level: 5 }) ? "is-active" : ""}
        size="sm"
        title="Heading 5"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleHeading({ level: 5 }).run()}
      >
        <Icon height={16} icon="mdi:format-header-5" width={16} />
      </Button>
      <Button
        className={editor.isActive("heading", { level: 6 }) ? "is-active" : ""}
        size="sm"
        title="Heading 6"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleHeading({ level: 6 }).run()}
      >
        <Icon height={16} icon="mdi:format-header-6" width={16} />
      </Button>
      <Button
        className={editor.isActive("italic") ? "bg-accent" : ""}
        size="sm"
        title="Italic"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleItalic().run()}
      >
        <Icon height={16} icon="mdi:format-italic" width={16} />
      </Button>
      <div className="h-6 w-px bg-border" />
      <Button
        className={editor.isActive("bulletList") ? "bg-accent" : ""}
        size="sm"
        title="Bullet List"
        type="button"
        variant="ghost"
        onPress={() => {
          editor.chain().focus().toggleBulletList().run();
        }}
      >
        <Icon height={16} icon="mdi:format-list-bulleted" width={16} />
      </Button>
      <Button
        className={editor.isActive("orderedList") ? "bg-accent" : ""}
        size="sm"
        title="Ordered List"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().toggleOrderedList().run()}
      >
        <Icon height={16} icon="mdi:format-list-numbered" width={16} />
      </Button>

      <Button
        disabled={!editor.can().undo()}
        size="sm"
        title="Undo"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().undo().run()}
      >
        <Icon height={16} icon="mdi:undo" width={16} />
      </Button>
      <Button
        disabled={!editor.can().redo()}
        size="sm"
        title="Redo"
        type="button"
        variant="ghost"
        onPress={() => editor.chain().focus().redo().run()}
      >
        <Icon height={16} icon="mdi:redo" width={16} />
      </Button>
    </div>
  );
};
