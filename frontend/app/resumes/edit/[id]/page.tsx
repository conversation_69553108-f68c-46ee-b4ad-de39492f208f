import { getResume } from "@/actions/resume";
import { getTemplates } from "@/actions/templates";
import { ResumeEditForm } from "@/components/resume/resume-edit-form";
import { ResumePreview } from "@/components/resume/resume-preview";
import { ResumeCustomizationProvider } from "@/contexts/ResumeCustomizationContext";
export default async function EditResumePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params; // Destructure id from params
  const resumeId = Number(id);
  const resume = await getResume(resumeId);

  const templates = await getTemplates();

  if (!resume) {
    return <div>Resume not found</div>;
  }

  return (
    <ResumeCustomizationProvider resume={resume}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 h-full">
        <div className="order-2 lg:order-1 col-span-2 h-full">
          <ResumePreview
            className="h-full"
            resume={resume}
            showControls={true}
            showTemplateSelector={true}
            templates={templates}
          />
        </div>
        <div className="order-1 lg:order-2 col-span-1 w-full py-4 overflow-auto">
          <ResumeEditForm data={resume} />
        </div>
      </div>
    </ResumeCustomizationProvider>
  );
}
