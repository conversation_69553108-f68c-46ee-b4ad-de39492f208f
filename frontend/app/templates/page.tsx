import { getTemplates } from "@/actions/templates";
import { Template<PERSON><PERSON><PERSON> } from "@/components/resume/templates";
import { TemplateGallery } from "@/components/templates/TemplateGallery";
import { ResumeCustomizationProvider } from "@/contexts/ResumeCustomizationContext";
import { createSampleResumeData } from "@/lib/sample-resume-data";

export default async function TemplatesPage() {
  const templates = await getTemplates();

  const sampleResume = createSampleResumeData();

  return (
    <ResumeCustomizationProvider>
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 w-full">
        <div className="lg:col-span-2">
          <h1 className="text-3xl font-bold mb-4">Resume Templates</h1>
          <p className="text-gray-600 mb-6">
            Choose from our collection of ATS-friendly resume templates. All
            templates are optimized for applicant tracking systems while
            maintaining professional appearance.
          </p>
          <TemplateGallery templates={templates} />
        </div>

        <div className="col-span-3">
          {/* Template Preview */}
          <div className="w-full">
            <div className="preview-container bg-gray-100 p-6 rounded-lg">
              <div
                className="bg-white shadow-lg mx-auto"
                style={{ width: "8.5in", minHeight: "11in" }}
              >
                <TemplateRenderer
                  className="w-full h-full"
                  resume={sampleResume}
                  templates={templates}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </ResumeCustomizationProvider>
  );
}
