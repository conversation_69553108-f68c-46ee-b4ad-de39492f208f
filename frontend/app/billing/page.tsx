import React from "react";
import { getUserSubscriptions, getUserPayments } from "@/actions/payments";
import { BillingDashboard } from "@/components/payments/billing-dashboard";
import { PaymentMethods } from "@/components/payments/payment-methods";
import { title } from "@/components/primitives";
import { Card, CardBody, CardHeader, Tabs, Tab } from "@heroui/react";
import { Icon } from "@iconify/react";

export default async function BillingPage() {
  const subscriptionsResult = await getUserSubscriptions();
  const paymentsResult = await getUserPayments();

  const subscriptions = subscriptionsResult.success ? subscriptionsResult.data : [];
  const payments = paymentsResult.success ? paymentsResult.data : [];
  const activeSubscription = subscriptions.find(sub => sub.status === 'active');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className={title({ size: "lg" })}>
          Billing & Subscriptions
        </h1>
        <p className="text-default-600 mt-2">
          Manage your subscription, payment methods, and billing history
        </p>
      </div>

      <Tabs aria-label="Billing options" className="w-full">
        <Tab
          key="overview"
          title={
            <div className="flex items-center space-x-2">
              <Icon icon="solar:chart-outline" />
              <span>Overview</span>
            </div>
          }
        >
          <Card>
            <CardBody>
              <BillingDashboard
                subscription={activeSubscription}
                payments={payments}
                onCancelSubscription={async () => {
                  "use server";
                  // This would be implemented with proper form actions
                }}
                onPauseSubscription={async () => {
                  "use server";
                  // This would be implemented with proper form actions
                }}
                onResumeSubscription={async () => {
                  "use server";
                  // This would be implemented with proper form actions
                }}
              />
            </CardBody>
          </Card>
        </Tab>

        <Tab
          key="payment-methods"
          title={
            <div className="flex items-center space-x-2">
              <Icon icon="solar:wallet-money-outline" />
              <span>Payment Methods</span>
            </div>
          }
        >
          <Card>
            <CardBody>
              <PaymentMethods
                paymentMethods={[]}
                onAddPaymentMethod={async () => {
                  "use server";
                  // This would return a setup intent client secret
                  return "";
                }}
                onRemovePaymentMethod={async (paymentMethodId: string) => {
                  "use server";
                  // This would remove the payment method
                }}
                onRefresh={async () => {
                  "use server";
                  // This would refresh the payment methods list
                }}
              />
            </CardBody>
          </Card>
        </Tab>

        <Tab
          key="invoices"
          title={
            <div className="flex items-center space-x-2">
              <Icon icon="solar:file-text-outline" />
              <span>Invoices</span>
            </div>
          }
        >
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold">Invoice History</h2>
            </CardHeader>
            <CardBody>
              <div className="text-center py-8 text-default-500">
                <Icon icon="solar:file-text-outline" className="text-4xl mb-2" />
                <p>No invoices found</p>
                <p className="text-sm">Invoices will appear here after your first payment</p>
              </div>
            </CardBody>
          </Card>
        </Tab>
      </Tabs>
    </div>
  );
}
