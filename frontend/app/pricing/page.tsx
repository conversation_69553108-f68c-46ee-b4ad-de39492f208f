"use client";

import React, { useState, useEffect } from "react";
import { title } from "@/components/primitives";
import { SubscriptionCard } from "@/components/payments/subscription-card";
import { StripeProvider } from "@/components/payments/stripe-provider";
import { PaymentForm } from "@/components/payments/payment-form";
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  useDisclosure,
  Button,
  Card,
  CardBody,
  Chip,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import toast from "react-hot-toast";

interface SubscriptionPlan {
  id: number;
  name: string;
  price_cents: number;
  currency: string;
  interval: string;
  features: string[];
  description: string;
  popular?: boolean;
}

export default function PricingPage() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [clientSecret, setClientSecret] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockPlans: SubscriptionPlan[] = [
      {
        id: 1,
        name: "Basic",
        price_cents: 999,
        currency: "usd",
        interval: "month",
        description: "Perfect for individuals getting started",
        features: [
          "3 Resume Templates",
          "Basic Customization",
          "PDF Export",
          "Email Support",
        ],
      },
      {
        id: 2,
        name: "Pro",
        price_cents: 1999,
        currency: "usd",
        interval: "month",
        description: "Best for professionals and job seekers",
        popular: true,
        features: [
          "All Resume Templates",
          "Advanced Customization",
          "PDF & Word Export",
          "Cover Letter Builder",
          "Priority Support",
          "ATS Optimization",
        ],
      },
      {
        id: 3,
        name: "Enterprise",
        price_cents: 4999,
        currency: "usd",
        interval: "month",
        description: "For teams and organizations",
        features: [
          "Everything in Pro",
          "Team Management",
          "Bulk Export",
          "Custom Branding",
          "API Access",
          "Dedicated Support",
        ],
      },
    ];
    setPlans(mockPlans);
  }, []);

  const handleSubscribe = async (planId: number) => {
    const plan = plans.find(p => p.id === planId);
    if (!plan) return;

    try {
      setIsLoading(true);
      setSelectedPlan(plan);

      // Mock API call - replace with actual subscription creation
      const response = await fetch("/api/subscriptions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          subscription_plan_id: planId,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create subscription");
      }

      const data = await response.json();
      setClientSecret(data.client_secret);
      onOpen();
    } catch (error) {
      toast.error("Failed to start subscription process");
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentSuccess = () => {
    toast.success("Subscription activated successfully!");
    onClose();
    setClientSecret("");
    setSelectedPlan(null);
    // Redirect to dashboard or refresh user data
  };

  const handlePaymentError = (error: string) => {
    toast.error(error);
  };

  return (
    <div className="container mx-auto px-4 py-12">
      {/* Header */}
      <div className="text-center mb-16">
        <h1 className={title({ size: "lg" })}>
          Choose Your Perfect Plan
        </h1>
        <p className="text-xl text-default-600 mt-4 max-w-2xl mx-auto">
          Create professional resumes with our powerful tools.
          Start free and upgrade when you're ready.
        </p>
      </div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
        {plans.map((plan) => (
          <SubscriptionCard
            key={plan.id}
            plan={plan}
            onSubscribe={handleSubscribe}
            isLoading={isLoading}
          />
        ))}
      </div>

      {/* Features Comparison */}
      <div className="max-w-4xl mx-auto mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">
          Compare Features
        </h2>
        <Card>
          <CardBody>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-default-200">
                    <th className="text-left py-4 px-4">Features</th>
                    <th className="text-center py-4 px-4">Basic</th>
                    <th className="text-center py-4 px-4">Pro</th>
                    <th className="text-center py-4 px-4">Enterprise</th>
                  </tr>
                </thead>
                <tbody>
                  {[
                    { feature: "Resume Templates", basic: "3", pro: "Unlimited", enterprise: "Unlimited" },
                    { feature: "PDF Export", basic: true, pro: true, enterprise: true },
                    { feature: "Word Export", basic: false, pro: true, enterprise: true },
                    { feature: "Cover Letter Builder", basic: false, pro: true, enterprise: true },
                    { feature: "ATS Optimization", basic: false, pro: true, enterprise: true },
                    { feature: "Team Management", basic: false, pro: false, enterprise: true },
                    { feature: "API Access", basic: false, pro: false, enterprise: true },
                    { feature: "Custom Branding", basic: false, pro: false, enterprise: true },
                  ].map((row, index) => (
                    <tr key={index} className="border-b border-default-100">
                      <td className="py-3 px-4 font-medium">{row.feature}</td>
                      <td className="py-3 px-4 text-center">
                        {typeof row.basic === "boolean" ? (
                          row.basic ? (
                            <Icon icon="solar:check-circle-bold" className="text-success text-xl mx-auto" />
                          ) : (
                            <Icon icon="solar:close-circle-bold" className="text-default-300 text-xl mx-auto" />
                          )
                        ) : (
                          row.basic
                        )}
                      </td>
                      <td className="py-3 px-4 text-center">
                        {typeof row.pro === "boolean" ? (
                          row.pro ? (
                            <Icon icon="solar:check-circle-bold" className="text-success text-xl mx-auto" />
                          ) : (
                            <Icon icon="solar:close-circle-bold" className="text-default-300 text-xl mx-auto" />
                          )
                        ) : (
                          row.pro
                        )}
                      </td>
                      <td className="py-3 px-4 text-center">
                        {typeof row.enterprise === "boolean" ? (
                          row.enterprise ? (
                            <Icon icon="solar:check-circle-bold" className="text-success text-xl mx-auto" />
                          ) : (
                            <Icon icon="solar:close-circle-bold" className="text-default-300 text-xl mx-auto" />
                          )
                        ) : (
                          row.enterprise
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* FAQ Section */}
      <div className="max-w-3xl mx-auto mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">
          Frequently Asked Questions
        </h2>
        <div className="space-y-4">
          {[
            {
              question: "Can I cancel my subscription anytime?",
              answer: "Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your current billing period."
            },
            {
              question: "Do you offer refunds?",
              answer: "We offer a 30-day money-back guarantee for all paid plans. If you're not satisfied, contact us for a full refund."
            },
            {
              question: "Can I change my plan later?",
              answer: "Absolutely! You can upgrade or downgrade your plan at any time. Changes will be prorated and reflected in your next billing cycle."
            },
            {
              question: "Is my data secure?",
              answer: "Yes, we use industry-standard encryption and security measures to protect your data. Your information is never shared with third parties."
            },
          ].map((faq, index) => (
            <Card key={index}>
              <CardBody>
                <h3 className="font-semibold mb-2">{faq.question}</h3>
                <p className="text-default-600">{faq.answer}</p>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="text-center">
        <Card className="bg-gradient-to-r from-primary-50 to-secondary-50 border-primary-200">
          <CardBody className="py-12">
            <h2 className="text-3xl font-bold mb-4">
              Ready to Create Your Perfect Resume?
            </h2>
            <p className="text-xl text-default-600 mb-6">
              Join thousands of professionals who have landed their dream jobs
            </p>
            <Button
              size="lg"
              color="primary"
              className="px-8"
              startContent={<Icon icon="solar:rocket-outline" />}
            >
              Start Free Trial
            </Button>
          </CardBody>
        </Card>
      </div>

      {/* Payment Modal */}
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        size="lg"
        classNames={{
          body: "py-6",
          backdrop: "bg-black/50 backdrop-opacity-40",
          base: "border-default-200 bg-content1",
          header: "border-b-[1px] border-default-200",
        }}
      >
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center gap-2">
              <Icon icon="solar:card-outline" className="text-xl" />
              Complete Your Subscription
              {selectedPlan && (
                <Chip color="primary" variant="flat" size="sm">
                  {selectedPlan.name}
                </Chip>
              )}
            </div>
          </ModalHeader>
          <ModalBody>
            {clientSecret && (
              <StripeProvider clientSecret={clientSecret}>
                <PaymentForm
                  onSuccess={handlePaymentSuccess}
                  onError={handlePaymentError}
                  submitText={`Subscribe to ${selectedPlan?.name}`}
                  showCard={false}
                />
              </StripeProvider>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </div>
  );
}
