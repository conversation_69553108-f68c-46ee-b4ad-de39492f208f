"use client";

import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useMemo,
} from "react";
import { Resume } from "@/types/resume";

// Color schemes
export const COLOR_SCHEMES = {
  blue: {
    id: "blue",
    name: "Professional Blue",
    primary: "#3B82F6",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  green: {
    id: "green",
    name: "<PERSON> Green",
    primary: "#10B981",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  purple: {
    id: "purple",
    name: "Creative Purple",
    primary: "#8B5CF6",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  red: {
    id: "red",
    name: "Bold Red",
    primary: "#EF4444",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  orange: {
    id: "orange",
    name: "Energetic Orange",
    primary: "#F97316",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  gray: {
    id: "gray",
    name: "<PERSON> Gray",
    primary: "#6B7280",
    text: "#1F2937",
    background: "#FFFFFF",
  },
};

// Font families
export const FONT_FAMILIES = {
  inter: {
    id: "inter",
    name: "Inter",
    family: "Inter, sans-serif",
    category: "modern",
  },
  roboto: {
    id: "roboto",
    name: "Roboto",
    family: "Roboto, sans-serif",
    category: "modern",
  },
  "open-sans": {
    id: "open-sans",
    name: "Open Sans",
    family: "Open Sans, sans-serif",
    category: "modern",
  },
  lato: {
    id: "lato",
    name: "Lato",
    family: "Lato, sans-serif",
    category: "modern",
  },
  "times-new-roman": {
    id: "times-new-roman",
    name: "Times New Roman",
    family: "Times New Roman, serif",
    category: "traditional",
  },
  georgia: {
    id: "georgia",
    name: "Georgia",
    family: "Georgia, serif",
    category: "traditional",
  },
  playfair: {
    id: "playfair",
    name: "Playfair Display",
    family: "Playfair Display, serif",
    category: "elegant",
  },
};

// Layout options
export const LAYOUT_OPTIONS = {
  spacing: {
    compact: { id: "compact", name: "Compact", value: 0.8 },
    normal: { id: "normal", name: "Normal", value: 1.0 },
    relaxed: { id: "relaxed", name: "Relaxed", value: 1.2 },
  },
  margins: {
    narrow: { id: "narrow", name: "Narrow", value: 0.5 },
    normal: { id: "normal", name: "Normal", value: 0.75 },
    wide: { id: "wide", name: "Wide", value: 1.0 },
  },
};

// Customization state interface
export interface ResumeCustomization {
  colorScheme:
  | keyof typeof COLOR_SCHEMES
  | (typeof COLOR_SCHEMES)[keyof typeof COLOR_SCHEMES];
  fontFamily:
  | keyof typeof FONT_FAMILIES
  | (typeof FONT_FAMILIES)[keyof typeof FONT_FAMILIES];
  spacing:
  | keyof typeof LAYOUT_OPTIONS.spacing
  | (typeof LAYOUT_OPTIONS.spacing)[keyof typeof LAYOUT_OPTIONS.spacing];
  margins:
  | keyof typeof LAYOUT_OPTIONS.margins
  | (typeof LAYOUT_OPTIONS.margins)[keyof typeof LAYOUT_OPTIONS.margins];
  sectionOrder: string[];
  customPrimaryColor?: string; // Optional custom primary color override
  customTextColor?: string; // Optional custom text color override
  customBackgroundColor?: string; // Optional custom background color override
}

// Default customization
const DEFAULT_CUSTOMIZATION: ResumeCustomization = {
  colorScheme: COLOR_SCHEMES.blue,
  fontFamily: FONT_FAMILIES.inter,
  spacing: LAYOUT_OPTIONS.spacing.normal,
  margins: LAYOUT_OPTIONS.margins.normal,
  sectionOrder: [
    "personal",
    "summary",
    "experience",
    "education",
    "skills",
    "languages",
    "certifications",
  ],
};

// Context interface
interface ResumeCustomizationContextType {
  updateCustomization: (updates: Partial<ResumeCustomization>) => void;
  resetCustomization: () => void;
  colorScheme: (typeof COLOR_SCHEMES)[keyof typeof COLOR_SCHEMES];
  fontFamily: (typeof FONT_FAMILIES)[keyof typeof FONT_FAMILIES];
  spacing: (typeof LAYOUT_OPTIONS.spacing)[keyof typeof LAYOUT_OPTIONS.spacing];
  margins: (typeof LAYOUT_OPTIONS.margins)[keyof typeof LAYOUT_OPTIONS.margins];
  selectedTemplateId: number;
  customTextColor?: string;
  customBackgroundColor?: string;
  customPrimaryColor?: string;
  setSelectedTemplateId: (templateId: number) => void;
}

// Create context
const ResumeCustomizationContext = createContext<
  ResumeCustomizationContextType | undefined
>(undefined);

export const initCustomizationFromResume = (
  resume: Resume,
): ResumeCustomization => {
  return {
    colorScheme: resume.color_scheme
      ? COLOR_SCHEMES[resume.color_scheme as keyof typeof COLOR_SCHEMES] ||
      COLOR_SCHEMES.blue
      : COLOR_SCHEMES.blue,
    fontFamily: resume.font_family
      ? FONT_FAMILIES[resume.font_family as keyof typeof FONT_FAMILIES] ||
      FONT_FAMILIES.inter
      : FONT_FAMILIES.inter,
    spacing: resume.spacing
      ? LAYOUT_OPTIONS.spacing[
      resume.spacing as keyof typeof LAYOUT_OPTIONS.spacing
      ] || LAYOUT_OPTIONS.spacing.normal
      : LAYOUT_OPTIONS.spacing.normal,
    margins: resume.margins
      ? LAYOUT_OPTIONS.margins[
      resume.margins as keyof typeof LAYOUT_OPTIONS.margins
      ] || LAYOUT_OPTIONS.margins.normal
      : LAYOUT_OPTIONS.margins.normal,
    sectionOrder: [
      "personal",
      "summary",
      "experience",
      "education",
      "skills",
      "languages",
      "certifications",
    ],
    customPrimaryColor: resume.custom_primary_color,
    customTextColor: resume.custom_text_color,
    customBackgroundColor: resume.custom_background_color,
  };
};

// Provider component
export const ResumeCustomizationProvider: React.FC<{
  children: ReactNode;
  resume: Resume;
}> = ({ children, resume }) => {
  const [customization, setCustomization] = useState<ResumeCustomization>(
    initCustomizationFromResume(resume),
  );
  const [selectedTemplateId, setSelectedTemplateId] = useState<number>();

  const updateCustomization = (updates: Partial<ResumeCustomization>) => {
    console.log("Updating customization:", updates, customization);

    setCustomization((prev) => ({ ...prev, ...updates }));
  };

  const resetCustomization = () => {
    setCustomization(DEFAULT_CUSTOMIZATION);
  };

  const colorScheme = useMemo(
    () => customization.colorScheme,
    [customization.colorScheme],
  );

  const fontFamily = useMemo(
    () => customization.fontFamily,
    [customization.fontFamily],
  );

  const spacing = useMemo(() => customization.spacing, [customization.spacing]);

  const margins = useMemo(() => customization.margins, [customization.margins]);

  const value = useMemo(() => {
    return {
      updateCustomization,
      resetCustomization,
      colorScheme,
      fontFamily,
      spacing,
      margins,
      selectedTemplateId,
      setSelectedTemplateId,
      customTextColor: customization.customTextColor,
      customBackgroundColor: customization.customBackgroundColor,
      customPrimaryColor: customization.customPrimaryColor,
    };
  }, [customization, selectedTemplateId]);

  return (
    <ResumeCustomizationContext.Provider value={value}>
      {children}
    </ResumeCustomizationContext.Provider>
  );
};

// Hook to use the context
export const useResumeCustomization = () => {
  const context = useContext(ResumeCustomizationContext);
  if (context === undefined) {
    throw new Error(
      "useResumeCustomization must be used within a ResumeCustomizationProvider",
    );
  }
  return context;
};
