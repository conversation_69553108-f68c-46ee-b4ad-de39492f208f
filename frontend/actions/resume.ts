"use server";

import { routes } from "@/config/path-constants";
import { apiClient } from "@/lib/api";
import { isAuthenticated } from "@/lib/auth";
import { formatError, getErrorsForForm } from "@/lib/format-error";
import { ActionResult } from "@/types";
import { Resume } from "@/types/resume";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";

export const createResume = async (resume: any) => {
  const isLoggedIn = await isAuthenticated();

  if (!isLoggedIn) {
    redirect("/login");
  }

  const res = await apiClient<Resume>(routes.fetchResumesPath(), {
    method: "POST",
    body: resume,
  });

  if (res.success) {
    const path = routes.resumeEditPath(res.data.id);
    redirect(path);
  }
};

export async function getResume(id: number) {
  const isLoggedIn = await isAuthenticated();

  if (!isLoggedIn) {
    redirect("/login");
  }

  const res = await apiClient<Resume>(routes.fetchResumePath(id));

  if (res.success) {
    return res.data;
  }
}

export const editResume = async (
  prevState: ActionResult,
  formData: FormData,
) => {
  try {
    const isLoggedIn = await isAuthenticated();

    const id = formData.get("id");

    if (!isLoggedIn) {
      redirect("/login");
    }

    const res = await apiClient<Resume>(routes.fetchResumePath(Number(id)), {
      method: "PATCH",
      body: formData,
    });

    if (!res.success) {
      return {
        errors: getErrorsForForm("Failed to sign in"),
      };
    }

    return {
      data: res.data,
      errors: {
        fieldErrors: {},
        formErrors: [],
      },
    };
  } catch (error) {
    return {
      errors: formatError(error),
    };
  }
};

export const listResumes = async () => {
  const isLoggedIn = await isAuthenticated();

  if (!isLoggedIn) {
    redirect("/login");
  }

  const res = await apiClient<Resume[]>(routes.fetchResumesPath());

  if (res.success) {
    return res.data;
  }
};

export const deleteNestedItem = async (
  route: string,
  id: number | string,
  resumeId: number | string,
) => {
  const isLoggedIn = await isAuthenticated();

  if (!isLoggedIn) {
    redirect("/login");
  }

  try {
    const res = await apiClient(`${route}/${id}`, {
      method: "DELETE",
    });

    if (res.success) {
      revalidatePath(`/resumes/edit/${resumeId}`);
      return { success: true };
    }
  } catch {
    return { success: false, error: "Failed to delete item." };
  }
};

export const updateNestedItemOrder = async (
  route: string,
  oldIndex: number,
  newIndex: number,
  resumeId: number | string,
) => {
  const isLoggedIn = await isAuthenticated();

  if (!isLoggedIn) {
    redirect("/login");
  }

  try {
    const res = await apiClient(`${route}/reorder`, {
      method: "PATCH",
      body: JSON.stringify({
        oldIndex,
        newIndex,
      }),
    });

    if (res.success) {
      revalidatePath(`/resumes/edit/${resumeId}`);
      return { success: true };
    }
  } catch {
    return { success: false, error: "Failed to reorder items." };
  }
};
