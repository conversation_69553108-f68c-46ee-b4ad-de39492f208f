"use server";

import { routes } from "@/config/path-constants";
import { apiClient } from "@/lib/api";
import { isAuthenticated } from "@/lib/auth";
import { formatError } from "@/lib/format-error";
import { ActionResult } from "@/types";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";

export interface SubscriptionPlan {
  id: number;
  name: string;
  stripe_price_id: string;
  price_cents: number;
  currency: string;
  interval: string;
  features: string[];
  description?: string;
  active: boolean;
}

export interface Subscription {
  id: number;
  status: string;
  current_period_start: string;
  current_period_end: string;
  trial_start?: string;
  trial_end?: string;
  canceled_at?: string;
  subscription_plan: SubscriptionPlan;
}

export interface Payment {
  id: number;
  amount_cents: number;
  currency: string;
  status: string;
  description: string;
  paid_at: string;
  payment_method_type: string;
  subscription?: Subscription;
}

// Subscription actions
export const getSubscriptionPlans = async (): Promise<ActionResult<SubscriptionPlan[]>> => {
  try {
    const res = await apiClient<SubscriptionPlan[]>("/subscriptions/plans");
    
    if (res.success) {
      return { success: true, data: res.data };
    } else {
      return { success: false, error: res.error || "Failed to fetch subscription plans" };
    }
  } catch (error) {
    return { success: false, error: formatError(error) };
  }
};

export const getUserSubscriptions = async (): Promise<ActionResult<Subscription[]>> => {
  const isLoggedIn = await isAuthenticated();
  
  if (!isLoggedIn) {
    redirect("/login");
  }

  try {
    const res = await apiClient<Subscription[]>("/subscriptions");
    
    if (res.success) {
      return { success: true, data: res.data };
    } else {
      return { success: false, error: res.error || "Failed to fetch subscriptions" };
    }
  } catch (error) {
    return { success: false, error: formatError(error) };
  }
};

export const createSubscription = async (planId: number): Promise<ActionResult<{ subscription: Subscription; client_secret: string }>> => {
  const isLoggedIn = await isAuthenticated();
  
  if (!isLoggedIn) {
    redirect("/login");
  }

  try {
    const res = await apiClient<{ subscription: Subscription; client_secret: string }>("/subscriptions", {
      method: "POST",
      body: { subscription_plan_id: planId },
    });
    
    if (res.success) {
      revalidatePath("/billing");
      return { success: true, data: res.data };
    } else {
      return { success: false, error: res.error || "Failed to create subscription" };
    }
  } catch (error) {
    return { success: false, error: formatError(error) };
  }
};

export const cancelSubscription = async (subscriptionId: number): Promise<ActionResult<void>> => {
  const isLoggedIn = await isAuthenticated();
  
  if (!isLoggedIn) {
    redirect("/login");
  }

  try {
    const res = await apiClient(`/subscriptions/${subscriptionId}/cancel`, {
      method: "POST",
    });
    
    if (res.success) {
      revalidatePath("/billing");
      return { success: true, data: undefined };
    } else {
      return { success: false, error: res.error || "Failed to cancel subscription" };
    }
  } catch (error) {
    return { success: false, error: formatError(error) };
  }
};

export const pauseSubscription = async (subscriptionId: number): Promise<ActionResult<void>> => {
  const isLoggedIn = await isAuthenticated();
  
  if (!isLoggedIn) {
    redirect("/login");
  }

  try {
    const res = await apiClient(`/subscriptions/${subscriptionId}/pause`, {
      method: "POST",
    });
    
    if (res.success) {
      revalidatePath("/billing");
      return { success: true, data: undefined };
    } else {
      return { success: false, error: res.error || "Failed to pause subscription" };
    }
  } catch (error) {
    return { success: false, error: formatError(error) };
  }
};

export const resumeSubscription = async (subscriptionId: number): Promise<ActionResult<void>> => {
  const isLoggedIn = await isAuthenticated();
  
  if (!isLoggedIn) {
    redirect("/login");
  }

  try {
    const res = await apiClient(`/subscriptions/${subscriptionId}/resume`, {
      method: "POST",
    });
    
    if (res.success) {
      revalidatePath("/billing");
      return { success: true, data: undefined };
    } else {
      return { success: false, error: res.error || "Failed to resume subscription" };
    }
  } catch (error) {
    return { success: false, error: formatError(error) };
  }
};

// Payment actions
export const getUserPayments = async (): Promise<ActionResult<Payment[]>> => {
  const isLoggedIn = await isAuthenticated();
  
  if (!isLoggedIn) {
    redirect("/login");
  }

  try {
    const res = await apiClient<Payment[]>("/payments");
    
    if (res.success) {
      return { success: true, data: res.data };
    } else {
      return { success: false, error: res.error || "Failed to fetch payments" };
    }
  } catch (error) {
    return { success: false, error: formatError(error) };
  }
};

export const createPaymentIntent = async (
  amount_cents: number,
  currency: string = "usd",
  description?: string
): Promise<ActionResult<{ payment: Payment; client_secret: string }>> => {
  const isLoggedIn = await isAuthenticated();
  
  if (!isLoggedIn) {
    redirect("/login");
  }

  try {
    const res = await apiClient<{ payment: Payment; client_secret: string }>("/payments/create_intent", {
      method: "POST",
      body: { amount_cents, currency, description },
    });
    
    if (res.success) {
      revalidatePath("/billing");
      return { success: true, data: res.data };
    } else {
      return { success: false, error: res.error || "Failed to create payment intent" };
    }
  } catch (error) {
    return { success: false, error: formatError(error) };
  }
};

export const createSetupIntent = async (): Promise<ActionResult<{ client_secret: string }>> => {
  const isLoggedIn = await isAuthenticated();
  
  if (!isLoggedIn) {
    redirect("/login");
  }

  try {
    const res = await apiClient<{ client_secret: string }>("/payments/setup_intent");
    
    if (res.success) {
      return { success: true, data: res.data };
    } else {
      return { success: false, error: res.error || "Failed to create setup intent" };
    }
  } catch (error) {
    return { success: false, error: formatError(error) };
  }
};

export const getPaymentMethods = async (): Promise<ActionResult<{ payment_methods: any[] }>> => {
  const isLoggedIn = await isAuthenticated();
  
  if (!isLoggedIn) {
    redirect("/login");
  }

  try {
    const res = await apiClient<{ payment_methods: any[] }>("/payments/payment_methods");
    
    if (res.success) {
      return { success: true, data: res.data };
    } else {
      return { success: false, error: res.error || "Failed to fetch payment methods" };
    }
  } catch (error) {
    return { success: false, error: formatError(error) };
  }
};

export const detachPaymentMethod = async (paymentMethodId: string): Promise<ActionResult<void>> => {
  const isLoggedIn = await isAuthenticated();
  
  if (!isLoggedIn) {
    redirect("/login");
  }

  try {
    const res = await apiClient(`/payments/payment_methods/${paymentMethodId}`, {
      method: "DELETE",
    });
    
    if (res.success) {
      revalidatePath("/billing");
      return { success: true, data: undefined };
    } else {
      return { success: false, error: res.error || "Failed to remove payment method" };
    }
  } catch (error) {
    return { success: false, error: formatError(error) };
  }
};
