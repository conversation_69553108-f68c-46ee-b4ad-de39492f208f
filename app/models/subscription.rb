class Subscription < ApplicationRecord
  belongs_to :user
  belongs_to :subscription_plan
  has_many :payments, dependent: :destroy

  validates :stripe_subscription_id, presence: true, uniqueness: true
  validates :stripe_customer_id, presence: true
  validates :status, presence: true
  validates :quantity, presence: true, numericality: { greater_than: 0 }

  # Serialize metadata as JSON
  serialize :metadata, JSON

  # Status constants
  STATUSES = %w[
    incomplete incomplete_expired trialing active past_due canceled unpaid paused
  ].freeze

  validates :status, inclusion: { in: STATUSES }

  scope :active, -> { where(status: 'active') }
  scope :canceled, -> { where(status: 'canceled') }
  scope :past_due, -> { where(status: 'past_due') }
  scope :trialing, -> { where(status: 'trialing') }

  def active?
    status == 'active'
  end

  def canceled?
    status == 'canceled'
  end

  def past_due?
    status == 'past_due'
  end

  def trialing?
    status == 'trialing'
  end

  def on_trial?
    trialing? && trial_end && trial_end > Time.current
  end

  def trial_days_remaining
    return 0 unless on_trial?
    
    ((trial_end - Time.current) / 1.day).ceil
  end

  def current_period_days_remaining
    return 0 unless current_period_end
    
    days = ((current_period_end - Time.current) / 1.day).ceil
    [days, 0].max
  end

  def next_billing_date
    current_period_end
  end

  def total_amount_cents
    subscription_plan.price_cents * quantity
  end

  def total_amount_in_dollars
    total_amount_cents / 100.0
  end

  def cancel!
    stripe_subscription = Stripe::Subscription.retrieve(stripe_subscription_id)
    stripe_subscription.cancel
    
    update!(
      status: 'canceled',
      canceled_at: Time.current
    )
  rescue Stripe::StripeError => e
    Rails.logger.error "Failed to cancel subscription #{stripe_subscription_id}: #{e.message}"
    raise e
  end

  def pause!
    stripe_subscription = Stripe::Subscription.retrieve(stripe_subscription_id)
    stripe_subscription.pause_collection = { behavior: 'void' }
    stripe_subscription.save
    
    update!(status: 'paused')
  rescue Stripe::StripeError => e
    Rails.logger.error "Failed to pause subscription #{stripe_subscription_id}: #{e.message}"
    raise e
  end

  def resume!
    stripe_subscription = Stripe::Subscription.retrieve(stripe_subscription_id)
    stripe_subscription.pause_collection = nil
    stripe_subscription.save
    
    # Status will be updated via webhook
  rescue Stripe::StripeError => e
    Rails.logger.error "Failed to resume subscription #{stripe_subscription_id}: #{e.message}"
    raise e
  end

  def to_stripe_subscription
    Stripe::Subscription.retrieve(stripe_subscription_id)
  rescue Stripe::StripeError => e
    Rails.logger.error "Failed to retrieve Stripe subscription #{stripe_subscription_id}: #{e.message}"
    nil
  end

  def sync_with_stripe!
    stripe_subscription = to_stripe_subscription
    return unless stripe_subscription

    update!(
      status: stripe_subscription.status,
      current_period_start: Time.at(stripe_subscription.current_period_start),
      current_period_end: Time.at(stripe_subscription.current_period_end),
      trial_start: stripe_subscription.trial_start ? Time.at(stripe_subscription.trial_start) : nil,
      trial_end: stripe_subscription.trial_end ? Time.at(stripe_subscription.trial_end) : nil,
      canceled_at: stripe_subscription.canceled_at ? Time.at(stripe_subscription.canceled_at) : nil
    )
  end
end
