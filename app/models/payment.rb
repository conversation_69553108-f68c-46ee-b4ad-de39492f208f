class Payment < ApplicationRecord
  belongs_to :user
  belongs_to :subscription, optional: true

  validates :stripe_payment_intent_id, presence: true, uniqueness: true
  validates :amount_cents, presence: true, numericality: { greater_than: 0 }
  validates :currency, presence: true
  validates :status, presence: true

  # Serialize metadata as JSON
  serialize :metadata, JSON

  # Status constants based on Stripe PaymentIntent statuses
  STATUSES = %w[
    requires_payment_method requires_confirmation requires_action
    processing requires_capture canceled succeeded
  ].freeze

  validates :status, inclusion: { in: STATUSES }

  scope :succeeded, -> { where(status: 'succeeded') }
  scope :failed, -> { where(status: 'canceled') }
  scope :pending, -> { where(status: ['requires_payment_method', 'requires_confirmation', 'requires_action', 'processing']) }
  scope :recent, -> { order(created_at: :desc) }

  def succeeded?
    status == 'succeeded'
  end

  def failed?
    status == 'canceled'
  end

  def pending?
    %w[requires_payment_method requires_confirmation requires_action processing].include?(status)
  end

  def amount_in_dollars
    amount_cents / 100.0
  end

  def formatted_amount
    "$#{'%.2f' % amount_in_dollars}"
  end

  def payment_method_display
    case payment_method_type
    when 'card'
      'Credit Card'
    when 'bank_transfer'
      'Bank Transfer'
    when 'sepa_debit'
      'SEPA Direct Debit'
    else
      payment_method_type&.humanize || 'Unknown'
    end
  end

  def to_stripe_payment_intent
    Stripe::PaymentIntent.retrieve(stripe_payment_intent_id)
  rescue Stripe::StripeError => e
    Rails.logger.error "Failed to retrieve Stripe payment intent #{stripe_payment_intent_id}: #{e.message}"
    nil
  end

  def sync_with_stripe!
    payment_intent = to_stripe_payment_intent
    return unless payment_intent

    update!(
      status: payment_intent.status,
      amount_cents: payment_intent.amount,
      currency: payment_intent.currency,
      paid_at: payment_intent.charges.data.first&.created ? Time.at(payment_intent.charges.data.first.created) : nil,
      stripe_charge_id: payment_intent.charges.data.first&.id,
      payment_method_type: payment_intent.charges.data.first&.payment_method_details&.type,
      failure_reason: payment_intent.last_payment_error&.message
    )
  end

  def refund!(amount_cents = nil)
    refund_amount = amount_cents || self.amount_cents
    
    charge = Stripe::Charge.retrieve(stripe_charge_id) if stripe_charge_id
    return unless charge

    Stripe::Refund.create(
      charge: stripe_charge_id,
      amount: refund_amount
    )
  rescue Stripe::StripeError => e
    Rails.logger.error "Failed to refund payment #{stripe_payment_intent_id}: #{e.message}"
    raise e
  end
end
