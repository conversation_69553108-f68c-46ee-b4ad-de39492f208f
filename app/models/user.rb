class User < ApplicationRecord
  has_secure_password
  has_many :sessions, dependent: :destroy
  has_many :resumes, dependent: :destroy
  has_many :subscriptions, dependent: :destroy
  has_many :payments, dependent: :destroy
  has_one :active_subscription, -> { where(status: 'active') }, class_name: 'Subscription'

  normalizes :email_address, with: ->(e) { e.strip.downcase }

  def subscribed?
    active_subscription.present?
  end

  def subscription_plan
    active_subscription&.subscription_plan
  end

  def stripe_customer_id
    active_subscription&.stripe_customer_id
  end

  def create_stripe_customer!
    return stripe_customer_id if stripe_customer_id

    customer = Stripe::Customer.create(
      email: email_address,
      name: "#{first_name} #{last_name}".strip.presence || email_address,
      metadata: {
        user_id: id
      }
    )

    customer.id
  rescue Stripe::StripeError => e
    Rails.logger.error "Failed to create Stripe customer for user #{id}: #{e.message}"
    raise e
  end
end
