class SubscriptionPlan < ApplicationRecord
  has_many :subscriptions, dependent: :destroy
  has_many :users, through: :subscriptions

  validates :name, presence: true
  validates :stripe_price_id, presence: true, uniqueness: true
  validates :price_cents, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :currency, presence: true
  validates :interval, presence: true, inclusion: { in: %w[month year] }

  scope :active, -> { where(active: true) }
  scope :ordered, -> { order(:sort_order, :price_cents) }

  # Serialize features as JSON
  serialize :features, JSON

  def price
    Money.new(price_cents, currency)
  end

  def price_in_dollars
    price_cents / 100.0
  end

  def monthly_price_cents
    case interval
    when 'month'
      price_cents
    when 'year'
      (price_cents / 12.0).round
    else
      price_cents
    end
  end

  def features_list
    features || []
  end

  def popular?
    # You can implement logic to determine if this is a popular plan
    # For example, based on subscription count or manual flag
    false
  end

  def to_stripe_price
    Stripe::Price.retrieve(stripe_price_id)
  rescue Stripe::StripeError => e
    Rails.logger.error "Failed to retrieve Stripe price #{stripe_price_id}: #{e.message}"
    nil
  end
end
