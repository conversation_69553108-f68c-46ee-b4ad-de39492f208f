class StripeWebhookService
  def initialize(event)
    @event = event
  end

  def process
    case @event.type
    when 'customer.subscription.created'
      handle_subscription_created
    when 'customer.subscription.updated'
      handle_subscription_updated
    when 'customer.subscription.deleted'
      handle_subscription_deleted
    when 'customer.subscription.trial_will_end'
      handle_trial_will_end
    when 'invoice.payment_succeeded'
      handle_invoice_payment_succeeded
    when 'invoice.payment_failed'
      handle_invoice_payment_failed
    when 'invoice.upcoming'
      handle_invoice_upcoming
    when 'payment_intent.succeeded'
      handle_payment_intent_succeeded
    when 'payment_intent.payment_failed'
      handle_payment_intent_failed
    when 'payment_method.attached'
      handle_payment_method_attached
    when 'customer.created'
      handle_customer_created
    else
      Rails.logger.info "Unhandled Stripe webhook event: #{@event.type}"
    end
  end

  private

  def handle_subscription_created
    subscription_data = @event.data.object
    
    # Find existing subscription or create new one
    subscription = Subscription.find_by(stripe_subscription_id: subscription_data.id)
    
    if subscription
      update_subscription_from_stripe(subscription, subscription_data)
    else
      create_subscription_from_stripe(subscription_data)
    end

    Rails.logger.info "Subscription created: #{subscription_data.id}"
  end

  def handle_subscription_updated
    subscription_data = @event.data.object
    
    subscription = Subscription.find_by(stripe_subscription_id: subscription_data.id)
    return unless subscription

    update_subscription_from_stripe(subscription, subscription_data)
    
    # Send notification if subscription status changed significantly
    if subscription.status_changed? && ['canceled', 'past_due', 'unpaid'].include?(subscription.status)
      # TODO: Send email notification to user
      Rails.logger.info "Subscription status changed to #{subscription.status} for user #{subscription.user.email_address}"
    end

    Rails.logger.info "Subscription updated: #{subscription_data.id}"
  end

  def handle_subscription_deleted
    subscription_data = @event.data.object
    
    subscription = Subscription.find_by(stripe_subscription_id: subscription_data.id)
    return unless subscription

    subscription.update!(
      status: 'canceled',
      canceled_at: Time.current
    )

    # TODO: Send cancellation confirmation email
    Rails.logger.info "Subscription deleted: #{subscription_data.id}"
  end

  def handle_trial_will_end
    subscription_data = @event.data.object
    
    subscription = Subscription.find_by(stripe_subscription_id: subscription_data.id)
    return unless subscription

    # TODO: Send trial ending notification email
    Rails.logger.info "Trial will end for subscription: #{subscription_data.id}"
  end

  def handle_invoice_payment_succeeded
    invoice_data = @event.data.object
    
    subscription = Subscription.find_by(stripe_subscription_id: invoice_data.subscription)
    return unless subscription

    # Create payment record for successful invoice payment
    if invoice_data.charge
      create_payment_from_charge(invoice_data, subscription)
    end

    # Update subscription status if needed
    if subscription.status != 'active' && invoice_data.paid
      subscription.update!(status: 'active')
    end

    # TODO: Send payment confirmation email
    Rails.logger.info "Invoice payment succeeded: #{invoice_data.id}"
  end

  def handle_invoice_payment_failed
    invoice_data = @event.data.object
    
    subscription = Subscription.find_by(stripe_subscription_id: invoice_data.subscription)
    return unless subscription

    # Update subscription status
    subscription.update!(status: 'past_due')

    # TODO: Send payment failed notification email
    Rails.logger.info "Invoice payment failed: #{invoice_data.id}"
  end

  def handle_invoice_upcoming
    invoice_data = @event.data.object
    
    subscription = Subscription.find_by(stripe_subscription_id: invoice_data.subscription)
    return unless subscription

    # TODO: Send upcoming payment notification email
    Rails.logger.info "Upcoming invoice for subscription: #{invoice_data.subscription}"
  end

  def handle_payment_intent_succeeded
    payment_intent_data = @event.data.object
    
    payment = Payment.find_by(stripe_payment_intent_id: payment_intent_data.id)
    return unless payment

    payment.update!(
      status: 'succeeded',
      paid_at: Time.current,
      stripe_charge_id: payment_intent_data.charges.data.first&.id,
      payment_method_type: payment_intent_data.charges.data.first&.payment_method_details&.type
    )

    # TODO: Send payment confirmation email
    Rails.logger.info "Payment intent succeeded: #{payment_intent_data.id}"
  end

  def handle_payment_intent_failed
    payment_intent_data = @event.data.object
    
    payment = Payment.find_by(stripe_payment_intent_id: payment_intent_data.id)
    return unless payment

    payment.update!(
      status: 'canceled',
      failure_reason: payment_intent_data.last_payment_error&.message
    )

    # TODO: Send payment failed notification email
    Rails.logger.info "Payment intent failed: #{payment_intent_data.id}"
  end

  def handle_payment_method_attached
    payment_method_data = @event.data.object
    
    # TODO: Log payment method attachment or send notification
    Rails.logger.info "Payment method attached: #{payment_method_data.id}"
  end

  def handle_customer_created
    customer_data = @event.data.object
    
    # TODO: Update user record with Stripe customer ID if needed
    Rails.logger.info "Customer created: #{customer_data.id}"
  end

  def update_subscription_from_stripe(subscription, subscription_data)
    subscription.update!(
      status: subscription_data.status,
      current_period_start: Time.at(subscription_data.current_period_start),
      current_period_end: Time.at(subscription_data.current_period_end),
      trial_start: subscription_data.trial_start ? Time.at(subscription_data.trial_start) : nil,
      trial_end: subscription_data.trial_end ? Time.at(subscription_data.trial_end) : nil,
      canceled_at: subscription_data.canceled_at ? Time.at(subscription_data.canceled_at) : nil,
      quantity: subscription_data.quantity || 1
    )
  end

  def create_subscription_from_stripe(subscription_data)
    # Find user by customer ID
    user = User.joins(:subscriptions)
                .where(subscriptions: { stripe_customer_id: subscription_data.customer })
                .first
    
    return unless user

    # Find subscription plan by price ID
    price_id = subscription_data.items.data.first&.price&.id
    subscription_plan = SubscriptionPlan.find_by(stripe_price_id: price_id)
    
    return unless subscription_plan

    Subscription.create!(
      user: user,
      subscription_plan: subscription_plan,
      stripe_subscription_id: subscription_data.id,
      stripe_customer_id: subscription_data.customer,
      status: subscription_data.status,
      current_period_start: Time.at(subscription_data.current_period_start),
      current_period_end: Time.at(subscription_data.current_period_end),
      trial_start: subscription_data.trial_start ? Time.at(subscription_data.trial_start) : nil,
      trial_end: subscription_data.trial_end ? Time.at(subscription_data.trial_end) : nil,
      quantity: subscription_data.quantity || 1
    )
  end

  def create_payment_from_charge(invoice_data, subscription)
    charge = Stripe::Charge.retrieve(invoice_data.charge)
    
    Payment.create!(
      user: subscription.user,
      subscription: subscription,
      stripe_payment_intent_id: charge.payment_intent,
      stripe_charge_id: charge.id,
      amount_cents: invoice_data.amount_paid,
      currency: invoice_data.currency,
      status: 'succeeded',
      description: "Subscription payment for #{subscription.subscription_plan.name}",
      paid_at: Time.at(charge.created),
      payment_method_type: charge.payment_method_details&.type
    )
  rescue Stripe::StripeError => e
    Rails.logger.error "Failed to create payment from charge: #{e.message}"
  end
end
