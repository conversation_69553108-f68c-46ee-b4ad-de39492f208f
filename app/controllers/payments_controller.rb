class PaymentsController < ApplicationController
  before_action :require_authentication
  before_action :set_payment, only: [:show, :refund]

  # GET /payments
  def index
    @payments = Current.user.payments.includes(:subscription).recent.limit(50)
    render json: @payments.as_json(include: { subscription: { include: :subscription_plan } })
  end

  # GET /payments/1
  def show
    render json: @payment.as_json(include: { subscription: { include: :subscription_plan } })
  end

  # POST /payments/create_intent
  def create_intent
    amount_cents = params[:amount_cents].to_i
    currency = params[:currency] || 'usd'
    description = params[:description] || 'Payment'

    # Create or retrieve Stripe customer
    stripe_customer_id = Current.user.create_stripe_customer!

    # Create Stripe PaymentIntent
    payment_intent = Stripe::PaymentIntent.create(
      amount: amount_cents,
      currency: currency,
      customer: stripe_customer_id,
      description: description,
      automatic_payment_methods: { enabled: true },
      metadata: {
        user_id: Current.user.id
      }
    )

    # Create local payment record
    @payment = Current.user.payments.create!(
      stripe_payment_intent_id: payment_intent.id,
      amount_cents: amount_cents,
      currency: currency,
      status: payment_intent.status,
      description: description
    )

    render json: {
      payment: @payment,
      client_secret: payment_intent.client_secret
    }, status: :created

  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error creating payment intent: #{e.message}"
    render json: { error: e.message }, status: :unprocessable_entity
  rescue => e
    Rails.logger.error "Error creating payment intent: #{e.message}"
    render json: { error: 'Failed to create payment intent' }, status: :unprocessable_entity
  end

  # POST /payments/1/refund
  def refund
    refund_amount = params[:amount_cents]&.to_i
    
    @payment.refund!(refund_amount)
    render json: { message: 'Refund processed successfully' }
  rescue => e
    Rails.logger.error "Error processing refund: #{e.message}"
    render json: { error: 'Failed to process refund' }, status: :unprocessable_entity
  end

  # GET /payments/setup_intent
  def setup_intent
    # Create or retrieve Stripe customer
    stripe_customer_id = Current.user.create_stripe_customer!

    # Create SetupIntent for saving payment methods
    setup_intent = Stripe::SetupIntent.create(
      customer: stripe_customer_id,
      payment_method_types: ['card'],
      usage: 'off_session'
    )

    render json: {
      client_secret: setup_intent.client_secret
    }
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error creating setup intent: #{e.message}"
    render json: { error: e.message }, status: :unprocessable_entity
  end

  # GET /payments/payment_methods
  def payment_methods
    stripe_customer_id = Current.user.stripe_customer_id
    return render json: { payment_methods: [] } unless stripe_customer_id

    payment_methods = Stripe::PaymentMethod.list(
      customer: stripe_customer_id,
      type: 'card'
    )

    render json: { payment_methods: payment_methods.data }
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error fetching payment methods: #{e.message}"
    render json: { error: e.message }, status: :unprocessable_entity
  end

  # DELETE /payments/payment_methods/:payment_method_id
  def detach_payment_method
    payment_method = Stripe::PaymentMethod.retrieve(params[:payment_method_id])
    payment_method.detach

    render json: { message: 'Payment method removed successfully' }
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error detaching payment method: #{e.message}"
    render json: { error: e.message }, status: :unprocessable_entity
  end

  private

  def set_payment
    @payment = Current.user.payments.find(params[:id])
  end

  def payment_params
    params.require(:payment).permit(:amount_cents, :currency, :description)
  end
end
