module SubscriptionRequired
  extend ActiveSupport::Concern

  included do
    before_action :require_active_subscription, if: :subscription_required?
  end

  private

  def require_active_subscription
    unless Current.user&.subscribed?
      render json: { 
        error: 'Active subscription required',
        subscription_required: true,
        message: 'This feature requires an active subscription. Please upgrade your plan to continue.'
      }, status: :payment_required
    end
  end

  def subscription_required?
    # Override this method in controllers to specify when subscription is required
    false
  end

  def require_subscription_plan(plan_name)
    return unless Current.user&.subscribed?

    current_plan = Current.user.subscription_plan
    required_plans = Array(plan_name)

    unless required_plans.include?(current_plan&.name&.downcase)
      render json: {
        error: 'Insufficient subscription plan',
        current_plan: current_plan&.name,
        required_plans: required_plans,
        message: "This feature requires a #{required_plans.join(' or ')} subscription plan."
      }, status: :payment_required
    end
  end

  def subscription_feature_limit(feature_name, limit)
    return unless Current.user&.subscribed?

    # This would check feature usage against limits
    # Implementation depends on how you track feature usage
    case feature_name
    when 'resume_exports'
      # Check how many resumes user has exported this month
      # if user_exports_this_month >= limit
      #   render json: { error: 'Export limit reached' }, status: :payment_required
      # end
    when 'resume_templates'
      # Check how many templates user can access
      # if accessible_templates_count >= limit
      #   render json: { error: 'Template limit reached' }, status: :payment_required
      # end
    end
  end
end
