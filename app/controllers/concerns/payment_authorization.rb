module PaymentAuthorization
  extend ActiveSupport::Concern

  private

  def authorize_subscription_owner
    @subscription = Current.user.subscriptions.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Subscription not found or access denied' }, status: :not_found
  end

  def authorize_payment_owner
    @payment = Current.user.payments.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Payment not found or access denied' }, status: :not_found
  end

  def validate_subscription_plan
    @subscription_plan = SubscriptionPlan.active.find(params[:subscription_plan_id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Invalid subscription plan' }, status: :unprocessable_entity
  end

  def prevent_duplicate_active_subscription
    if Current.user.subscriptions.where(status: 'active').exists?
      render json: { 
        error: 'User already has an active subscription',
        message: 'Please cancel your current subscription before creating a new one.'
      }, status: :unprocessable_entity
    end
  end

  def validate_payment_amount
    amount_cents = params[:amount_cents].to_i
    
    if amount_cents <= 0
      render json: { error: 'Invalid payment amount' }, status: :unprocessable_entity
      return false
    end

    if amount_cents > 999999999 # $9,999,999.99 limit
      render json: { error: 'Payment amount too large' }, status: :unprocessable_entity
      return false
    end

    true
  end

  def validate_currency
    currency = params[:currency] || 'usd'
    supported_currencies = %w[usd eur gbp cad aud]
    
    unless supported_currencies.include?(currency.downcase)
      render json: { 
        error: 'Unsupported currency',
        supported_currencies: supported_currencies
      }, status: :unprocessable_entity
      return false
    end

    true
  end

  def rate_limit_payment_attempts
    # Implement rate limiting for payment attempts
    # This is a simple example - you might want to use Redis or a more sophisticated solution
    
    cache_key = "payment_attempts:#{Current.user.id}"
    attempts = Rails.cache.read(cache_key) || 0
    
    if attempts >= 5 # Max 5 payment attempts per hour
      render json: { 
        error: 'Too many payment attempts',
        message: 'Please wait before trying again.'
      }, status: :too_many_requests
      return false
    end

    Rails.cache.write(cache_key, attempts + 1, expires_in: 1.hour)
    true
  end

  def log_payment_activity(action, details = {})
    Rails.logger.info({
      event: 'payment_activity',
      action: action,
      user_id: Current.user&.id,
      user_email: Current.user&.email_address,
      timestamp: Time.current,
      details: details
    }.to_json)
  end

  def validate_refund_eligibility(payment)
    unless payment.succeeded?
      render json: { error: 'Only successful payments can be refunded' }, status: :unprocessable_entity
      return false
    end

    # Check if payment is within refund window (e.g., 30 days)
    if payment.paid_at < 30.days.ago
      render json: { error: 'Payment is outside the refund window' }, status: :unprocessable_entity
      return false
    end

    true
  end

  def validate_subscription_action(subscription, action)
    case action
    when 'cancel'
      unless subscription.active?
        render json: { error: 'Only active subscriptions can be canceled' }, status: :unprocessable_entity
        return false
      end
    when 'pause'
      unless subscription.active?
        render json: { error: 'Only active subscriptions can be paused' }, status: :unprocessable_entity
        return false
      end
    when 'resume'
      unless subscription.status == 'paused'
        render json: { error: 'Only paused subscriptions can be resumed' }, status: :unprocessable_entity
        return false
      end
    end

    true
  end
end
