class StripeWebhooksController < ApplicationController
  skip_before_action :verify_authenticity_token
  before_action :verify_stripe_signature

  def create
    StripeWebhookService.new(@event).process
    render json: { status: 'success' }
  rescue => e
    Rails.logger.error "Stripe webhook error: #{e.message}"
    render json: { error: e.message }, status: :unprocessable_entity
  end

  private

  def verify_stripe_signature
    payload = request.body.read
    sig_header = request.env['HTTP_STRIPE_SIGNATURE']
    endpoint_secret = Rails.configuration.stripe[:webhook_secret]

    begin
      @event = Stripe::Webhook.construct_event(payload, sig_header, endpoint_secret)
    rescue JSON::ParserError => e
      Rails.logger.error "Invalid JSON in Stripe webhook: #{e.message}"
      render json: { error: 'Invalid JSON' }, status: :bad_request and return
    rescue Stripe::SignatureVerificationError => e
      Rails.logger.error "Invalid Stripe signature: #{e.message}"
      render json: { error: 'Invalid signature' }, status: :bad_request and return
    end
  end
end
