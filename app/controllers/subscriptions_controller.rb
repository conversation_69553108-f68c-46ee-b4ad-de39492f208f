class SubscriptionsController < ApplicationController
  include PaymentAuthorization

  before_action :require_authentication
  before_action :authorize_subscription_owner, only: [:show, :cancel, :pause, :resume]
  before_action :validate_subscription_plan, only: [:create]
  before_action :prevent_duplicate_active_subscription, only: [:create]

  # GET /subscriptions
  def index
    @subscriptions = Current.user.subscriptions.includes(:subscription_plan).order(created_at: :desc)
    render json: @subscriptions.as_json(include: :subscription_plan)
  end

  # GET /subscriptions/1
  def show
    render json: @subscription.as_json(include: :subscription_plan)
  end

  # POST /subscriptions
  def create
    @subscription_plan = SubscriptionPlan.find(params[:subscription_plan_id])
    
    # Create or retrieve Stripe customer
    stripe_customer_id = Current.user.create_stripe_customer!
    
    # Create Stripe subscription
    stripe_subscription = Stripe::Subscription.create(
      customer: stripe_customer_id,
      items: [{ price: @subscription_plan.stripe_price_id }],
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent'],
      metadata: {
        user_id: Current.user.id,
        subscription_plan_id: @subscription_plan.id
      }
    )

    # Create local subscription record
    @subscription = Current.user.subscriptions.create!(
      subscription_plan: @subscription_plan,
      stripe_subscription_id: stripe_subscription.id,
      stripe_customer_id: stripe_customer_id,
      status: stripe_subscription.status,
      current_period_start: Time.at(stripe_subscription.current_period_start),
      current_period_end: Time.at(stripe_subscription.current_period_end),
      trial_start: stripe_subscription.trial_start ? Time.at(stripe_subscription.trial_start) : nil,
      trial_end: stripe_subscription.trial_end ? Time.at(stripe_subscription.trial_end) : nil
    )

    render json: {
      subscription: @subscription.as_json(include: :subscription_plan),
      client_secret: stripe_subscription.latest_invoice.payment_intent.client_secret
    }, status: :created

  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error creating subscription: #{e.message}"
    render json: { error: e.message }, status: :unprocessable_entity
  rescue => e
    Rails.logger.error "Error creating subscription: #{e.message}"
    render json: { error: 'Failed to create subscription' }, status: :unprocessable_entity
  end

  # POST /subscriptions/1/cancel
  def cancel
    return unless validate_subscription_action(@subscription, 'cancel')

    log_payment_activity('subscription_cancel_attempt', { subscription_id: @subscription.id })
    @subscription.cancel!
    log_payment_activity('subscription_canceled', { subscription_id: @subscription.id })
    render json: { message: 'Subscription canceled successfully' }
  rescue => e
    Rails.logger.error "Error canceling subscription: #{e.message}"
    render json: { error: 'Failed to cancel subscription' }, status: :unprocessable_entity
  end

  # POST /subscriptions/1/pause
  def pause
    return unless validate_subscription_action(@subscription, 'pause')

    log_payment_activity('subscription_pause_attempt', { subscription_id: @subscription.id })
    @subscription.pause!
    log_payment_activity('subscription_paused', { subscription_id: @subscription.id })
    render json: { message: 'Subscription paused successfully' }
  rescue => e
    Rails.logger.error "Error pausing subscription: #{e.message}"
    render json: { error: 'Failed to pause subscription' }, status: :unprocessable_entity
  end

  # POST /subscriptions/1/resume
  def resume
    return unless validate_subscription_action(@subscription, 'resume')

    log_payment_activity('subscription_resume_attempt', { subscription_id: @subscription.id })
    @subscription.resume!
    log_payment_activity('subscription_resumed', { subscription_id: @subscription.id })
    render json: { message: 'Subscription resumed successfully' }
  rescue => e
    Rails.logger.error "Error resuming subscription: #{e.message}"
    render json: { error: 'Failed to resume subscription' }, status: :unprocessable_entity
  end

  # GET /subscriptions/plans
  def plans
    @plans = SubscriptionPlan.active.ordered
    render json: @plans
  end

  private

  def subscription_params
    params.require(:subscription).permit(:subscription_plan_id)
  end
end
